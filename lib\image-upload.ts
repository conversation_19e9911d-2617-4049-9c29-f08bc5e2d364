import { supabase } from './supabase'

export interface ImageUploadResult {
  url: string | null
  error: string | null
}

export class ImageUploadManager {
  private static readonly BUCKET_NAME = 'pet-images'
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
  private static readonly ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']

  /**
   * Upload an image file to Supabase Storage
   */
  static async uploadPetImage(file: File, petId: string): Promise<ImageUploadResult> {
    try {
      // Validate file
      const validation = this.validateFile(file)
      if (!validation.isValid) {
        return { url: null, error: validation.error }
      }

      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        return { url: null, error: 'User not authenticated' }
      }

      // Create unique filename
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}/${petId}/${Date.now()}.${fileExt}`

      // Upload file
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        console.error('Upload error:', error)
        return { url: null, error: 'Failed to upload image' }
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(data.path)

      return { url: publicUrl, error: null }
    } catch (error) {
      console.error('Image upload error:', error)
      return { url: null, error: 'Failed to upload image' }
    }
  }

  /**
   * Delete an image from Supabase Storage
   */
  static async deletePetImage(imageUrl: string): Promise<boolean> {
    try {
      // Extract path from URL
      const url = new URL(imageUrl)
      const pathParts = url.pathname.split('/')
      const bucketIndex = pathParts.findIndex(part => part === this.BUCKET_NAME)
      
      if (bucketIndex === -1) {
        return false
      }

      const filePath = pathParts.slice(bucketIndex + 1).join('/')
      
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath])

      return !error
    } catch (error) {
      console.error('Image deletion error:', error)
      return false
    }
  }

  /**
   * Validate uploaded file
   */
  private static validateFile(file: File): { isValid: boolean; error: string } {
    if (!file) {
      return { isValid: false, error: 'No file selected' }
    }

    if (file.size > this.MAX_FILE_SIZE) {
      return { isValid: false, error: 'File size must be less than 5MB' }
    }

    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return { isValid: false, error: 'Only JPEG, PNG, and WebP images are allowed' }
    }

    return { isValid: true, error: '' }
  }

  /**
   * Resize image before upload (optional utility)
   */
  static async resizeImage(file: File, maxWidth: number = 800, maxHeight: number = 600): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width
            width = maxWidth
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height
            height = maxHeight
          }
        }

        canvas.width = width
        canvas.height = height

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height)
        
        canvas.toBlob((blob) => {
          if (blob) {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })
            resolve(resizedFile)
          } else {
            resolve(file)
          }
        }, file.type, 0.8)
      }

      img.src = URL.createObjectURL(file)
    })
  }
}
