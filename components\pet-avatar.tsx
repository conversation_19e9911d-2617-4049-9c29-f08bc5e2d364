'use client'

import { PawPrint } from 'lucide-react'
import Image from 'next/image'
import { cn } from '@/lib/utils'

interface PetAvatarProps {
  imageUrl?: string | null
  petName: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  showFallback?: boolean
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12', 
  lg: 'w-16 h-16',
  xl: 'w-24 h-24'
}

const iconSizes = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8', 
  xl: 'h-12 w-12'
}

export function PetAvatar({ 
  imageUrl, 
  petName, 
  size = 'md', 
  className,
  showFallback = true 
}: PetAvatarProps) {
  const sizeClass = sizeClasses[size]
  const iconSize = iconSizes[size]

  if (imageUrl) {
    return (
      <div className={cn(
        'relative rounded-full overflow-hidden bg-gray-100 flex-shrink-0',
        sizeClass,
        className
      )}>
        <Image
          src={imageUrl}
          alt={`${petName}'s photo`}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>
    )
  }

  if (!showFallback) {
    return null
  }

  return (
    <div className={cn(
      'rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0',
      sizeClass,
      className
    )}>
      <PawPrint className={cn('text-gray-400', iconSize)} />
    </div>
  )
}
