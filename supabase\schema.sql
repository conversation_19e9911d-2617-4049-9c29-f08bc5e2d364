-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create pets table
CREATE TABLE pets (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  species TEXT NOT NULL,
  breed TEXT,
  date_of_birth DATE,
  weight TEXT,
  color TEXT,
  notes TEXT,
  image_url TEXT,
  created_by UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create pet_owners table for sharing pets
CREATE TABLE pet_owners (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  pet_id UUID REFERENCES pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  role TEXT CHECK (role IN ('owner', 'caretaker')) NOT NULL DEFAULT 'caretaker',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(pet_id, user_id)
);

-- Create feeding_logs table
CREATE TABLE feeding_logs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  pet_id UUID REFERENCES pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  food_type TEXT NOT NULL,
  amount TEXT,
  notes TEXT,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create litter_logs table
CREATE TABLE litter_logs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  pet_id UUID REFERENCES pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  type TEXT CHECK (type IN ('scoop', 'deep-clean', 'full-change')) NOT NULL,
  notes TEXT,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create vaccinations table
CREATE TABLE vaccinations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  pet_id UUID REFERENCES pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  vaccine TEXT NOT NULL,
  date DATE NOT NULL,
  next_due DATE,
  veterinarian TEXT NOT NULL,
  lot_number TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notes table
CREATE TABLE notes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  pet_id UUID REFERENCES pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  topic TEXT NOT NULL,
  category TEXT NOT NULL DEFAULT 'general',
  description TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_pets_created_by ON pets(created_by);
CREATE INDEX idx_pet_owners_pet_id ON pet_owners(pet_id);
CREATE INDEX idx_pet_owners_user_id ON pet_owners(user_id);
CREATE INDEX idx_feeding_logs_pet_id ON feeding_logs(pet_id);
CREATE INDEX idx_feeding_logs_timestamp ON feeding_logs(timestamp DESC);
CREATE INDEX idx_litter_logs_pet_id ON litter_logs(pet_id);
CREATE INDEX idx_litter_logs_timestamp ON litter_logs(timestamp DESC);
CREATE INDEX idx_vaccinations_pet_id ON vaccinations(pet_id);
CREATE INDEX idx_vaccinations_date ON vaccinations(date DESC);
CREATE INDEX idx_notes_pet_id ON notes(pet_id);
CREATE INDEX idx_notes_timestamp ON notes(timestamp DESC);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners ENABLE ROW LEVEL SECURITY;
ALTER TABLE feeding_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE litter_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE vaccinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;

-- RLS Policies for profiles
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for pets
CREATE POLICY "Users can view pets they own or have access to" ON pets
  FOR SELECT USING (
    created_by = auth.uid() OR
    id IN (
      SELECT pet_id FROM pet_owners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create pets" ON pets
  FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Pet creators and owners can update pets" ON pets
  FOR UPDATE USING (
    created_by = auth.uid() OR
    id IN (
      SELECT pet_id FROM pet_owners 
      WHERE user_id = auth.uid() AND role = 'owner'
    )
  );

CREATE POLICY "Pet creators can delete pets" ON pets
  FOR DELETE USING (created_by = auth.uid());

-- RLS Policies for pet_owners
CREATE POLICY "Users can view pet ownership for accessible pets" ON pet_owners
  FOR SELECT USING (
    user_id = auth.uid() OR
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
    )
  );

CREATE POLICY "Pet creators can manage pet ownership" ON pet_owners
  FOR ALL USING (
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
    )
  );

-- RLS Policies for feeding_logs
CREATE POLICY "Users can view feeding logs for accessible pets" ON feeding_logs
  FOR SELECT USING (
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
      UNION
      SELECT pet_id FROM pet_owners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create feeding logs for accessible pets" ON feeding_logs
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
      UNION
      SELECT pet_id FROM pet_owners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own feeding logs" ON feeding_logs
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete own feeding logs" ON feeding_logs
  FOR DELETE USING (user_id = auth.uid());

-- RLS Policies for litter_logs (similar pattern)
CREATE POLICY "Users can view litter logs for accessible pets" ON litter_logs
  FOR SELECT USING (
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
      UNION
      SELECT pet_id FROM pet_owners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create litter logs for accessible pets" ON litter_logs
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
      UNION
      SELECT pet_id FROM pet_owners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own litter logs" ON litter_logs
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete own litter logs" ON litter_logs
  FOR DELETE USING (user_id = auth.uid());

-- RLS Policies for vaccinations
CREATE POLICY "Users can view vaccinations for accessible pets" ON vaccinations
  FOR SELECT USING (
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
      UNION
      SELECT pet_id FROM pet_owners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create vaccinations for accessible pets" ON vaccinations
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
      UNION
      SELECT pet_id FROM pet_owners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own vaccinations" ON vaccinations
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete own vaccinations" ON vaccinations
  FOR DELETE USING (user_id = auth.uid());

-- RLS Policies for notes
CREATE POLICY "Users can view notes for accessible pets" ON notes
  FOR SELECT USING (
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
      UNION
      SELECT pet_id FROM pet_owners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create notes for accessible pets" ON notes
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
      UNION
      SELECT pet_id FROM pet_owners WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own notes" ON notes
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete own notes" ON notes
  FOR DELETE USING (user_id = auth.uid());

-- Function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to automatically create pet ownership record when pet is created
CREATE OR REPLACE FUNCTION public.handle_new_pet()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.pet_owners (pet_id, user_id, role)
  VALUES (NEW.id, NEW.created_by, 'owner');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Trigger to create pet ownership record when pet is created
CREATE TRIGGER on_pet_created
  AFTER INSERT ON pets
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_pet();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pets_updated_at BEFORE UPDATE ON pets
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
