# Pet Sharing Setup Guide

This guide will help you complete the setup for the new pet sharing functionality with private shareable URLs.

## ✅ What's Already Done

- ✅ Database schema and functions created
- ✅ API routes implemented
- ✅ Storage functions added
- ✅ UI components created
- ✅ Share acceptance page built
- ✅ Missing Alert component added

## 🔧 Setup Steps Required

### 1. Run Database Migration

Execute the SQL migration in your Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of `supabase/migration_pet_sharing.sql`
4. Click **Run** to execute the migration

### 2. Add Service Role Key

You need to add your Supabase service role key to your environment variables:

1. Go to your Supabase project dashboard
2. Navigate to **Settings** > **API**
3. Copy the **service_role** key (not the anon key)
4. Update your `.env.local` file:

```env
SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key_here
```

⚠️ **Important**: The service role key is sensitive and should never be exposed to the client-side code.

### 3. Restart Development Server

After updating the environment variables:

```bash
npm run dev
```

## 🧪 Testing the Feature

### Test Share Link Generation

1. **Login** to your application
2. **Select a pet** you own (you must be the creator)
3. **Click the link button** (🔗) next to the share button
4. **Generate a new link** - you should see a dialog with share link management
5. **Copy the generated URL**

### Test Share Link Acceptance

1. **Open the share URL** in an incognito window or different browser
2. **Without logging in**: You should see the pet details and a login form
3. **After logging in**: You should see a confirmation page with:
   - Pet information
   - Owner details
   - Confirmation message: "UserName accepts PetName as a shared Species"
   - Confirm button

4. **Click Confirm** - you should be redirected to the main dashboard
5. **Verify access** - the shared pet should now appear in your pet list

### Test Share Management

1. **Return to the share link dialog** for the same pet
2. **View usage statistics** - should show the link was used once
3. **Test deactivation** - deactivate the link and try accessing it again
4. **Verify error handling** - deactivated links should show appropriate error messages

## 🎯 How It Works

### User Flow

1. **Pet Owner** generates a shareable URL via the link button (🔗)
2. **Pet Owner** shares the URL with another user
3. **Recipient** visits the URL and sees pet details
4. **Recipient** logs in (if not already authenticated)
5. **Recipient** confirms: "I accept [Pet] as a shared [Species]"
6. **System** grants caretaker access to the recipient
7. **Recipient** can now view and interact with the shared pet

### Security Features

- **Token expiration**: Links expire after 30 days
- **Usage tracking**: Monitor how many times links are accessed
- **Access control**: Only pet creators/owners can generate links
- **Deactivation**: Links can be revoked at any time
- **Authentication required**: Users must be logged in to accept shares

## 🔍 Troubleshooting

### "Module not found: Can't resolve '@/components/ui/alert'"
- ✅ **Fixed**: Alert component has been created

### API Routes Not Working
- Check that `SUPABASE_SERVICE_ROLE_KEY` is set correctly
- Verify the service role key is from the correct Supabase project
- Restart your development server after adding environment variables

### Database Functions Not Found
- Ensure you've run the complete migration script
- Check Supabase logs for any SQL execution errors
- Verify all functions were created successfully

### Share Links Not Generating
- Check browser console for API errors
- Verify user has owner/creator permissions for the pet
- Ensure database migration was successful

### Authentication Issues on Share Pages
- Verify Supabase auth configuration
- Check that redirect URLs are properly configured
- Ensure auth provider is wrapping the share page

## 🚀 Next Steps

Once everything is working:

1. **Test thoroughly** with different user accounts
2. **Monitor usage** through the share management interface
3. **Consider adding** email notifications for share invitations
4. **Set up cleanup** job to remove expired tokens periodically

## 📝 Files Modified/Created

### New Files
- `supabase/migration_pet_sharing.sql` - Database schema and functions
- `app/api/pets/[petId]/share/route.ts` - Share token API
- `app/api/share/[token]/route.ts` - Token validation and acceptance API
- `app/share/[token]/page.tsx` - Share acceptance page
- `components/ui/alert.tsx` - Missing UI component
- `SHARE_SETUP_GUIDE.md` - This setup guide

### Modified Files
- `lib/supabase.ts` - Added pet_share_tokens type definitions
- `lib/storage.ts` - Added share token management functions
- `components/pet-manager.tsx` - Added share link UI and functionality
- `.env.local` - Added service role key placeholder

The pet sharing feature is now ready for use! 🎉
