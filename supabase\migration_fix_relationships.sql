-- Fix the foreign key relationships that are missing
-- This will restore the proper relationships between tables

-- Step 1: Ensure all foreign key constraints exist properly

-- Fix pet_owners -> profiles relationship
ALTER TABLE pet_owners DROP CONSTRAINT IF EXISTS pet_owners_user_id_fkey;
ALTER TABLE pet_owners 
ADD CONSTRAINT pet_owners_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- Fix other table relationships to profiles
ALTER TABLE feeding_logs DROP CONSTRAINT IF EXISTS feeding_logs_user_id_fkey;
ALTER TABLE feeding_logs 
ADD CONSTRAINT feeding_logs_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

ALTER TABLE litter_logs DROP CONSTRAINT IF EXISTS litter_logs_user_id_fkey;
ALTER TABLE litter_logs 
ADD CONSTRAINT litter_logs_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

ALTER TABLE vaccinations DROP CONSTRAINT IF EXISTS vaccinations_user_id_fkey;
ALTER TABLE vaccinations 
ADD CONSTRAINT vaccinations_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

ALTER TABLE notes DROP CONSTRAINT IF EXISTS notes_user_id_fkey;
ALTER TABLE notes 
ADD CONSTRAINT notes_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- Step 2: Fix pets -> profiles relationship
ALTER TABLE pets DROP CONSTRAINT IF EXISTS pets_created_by_fkey;
ALTER TABLE pets 
ADD CONSTRAINT pets_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE CASCADE;

-- Step 3: Ensure pet_owners -> pets relationship exists
ALTER TABLE pet_owners DROP CONSTRAINT IF EXISTS pet_owners_pet_id_fkey;
ALTER TABLE pet_owners 
ADD CONSTRAINT pet_owners_pet_id_fkey 
FOREIGN KEY (pet_id) REFERENCES pets(id) ON DELETE CASCADE;

-- Step 4: Create any missing pet_owners records for existing pets
INSERT INTO pet_owners (pet_id, user_id, role)
SELECT p.id, p.created_by, 'owner'
FROM pets p
LEFT JOIN pet_owners po ON p.id = po.pet_id AND p.created_by = po.user_id
WHERE po.id IS NULL
ON CONFLICT DO NOTHING;

-- Step 5: Refresh the schema cache (this helps Supabase recognize the relationships)
NOTIFY pgrst, 'reload schema';

SELECT 'Foreign key relationships fixed' as status;
