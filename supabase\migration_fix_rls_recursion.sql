-- Migration to fix RLS policy infinite recursion
-- Run this in your Supabase SQL Editor

-- Drop the problematic policy that causes infinite recursion
DROP POLICY IF EXISTS "Users can view pet ownership for accessible pets" ON pet_owners;

-- Recreate the policy without the self-referencing part that causes recursion
CREATE POLICY "Users can view pet ownership for accessible pets" ON pet_owners
  FOR SELECT USING (
    user_id = auth.uid() OR
    pet_id IN (
      SELECT id FROM pets WHERE created_by = auth.uid()
    )
  );

-- Verify the policies are working correctly
-- This query should work without infinite recursion
SELECT 
  p.name,
  p.created_by,
  po.user_id,
  po.role
FROM pets p
LEFT JOIN pet_owners po ON p.id = po.pet_id
WHERE p.created_by = auth.uid() OR po.user_id = auth.uid()
ORDER BY p.created_at DESC
LIMIT 5;
