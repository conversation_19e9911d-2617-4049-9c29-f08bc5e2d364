'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth/auth-provider'
import { LoginForm } from '@/components/auth/login-form'
import { petStorage } from '@/lib/storage'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { PawPrint, Heart, AlertCircle, CheckCircle, Loader2 } from 'lucide-react'

interface ShareValidation {
  isValid: boolean
  pet?: {
    id: string
    name: string
    species: string
  }
  owner?: {
    name: string
    email: string
  }
  tokenId?: string
  error?: string
}

export default function SharePage() {
  const params = useParams()
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [validation, setValidation] = useState<ShareValidation | null>(null)
  const [loading, setLoading] = useState(true)
  const [accepting, setAccepting] = useState(false)
  const [accepted, setAccepted] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const token = params.token as string

  useEffect(() => {
    if (token) {
      validateToken()
    }
  }, [token])

  const validateToken = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await petStorage.validateShareToken(token)
      setValidation(result)
      
      if (!result.isValid) {
        setError(result.error || 'Invalid share link')
      }
    } catch (err) {
      console.error('Error validating token:', err)
      setError('Failed to validate share link')
    } finally {
      setLoading(false)
    }
  }

  const handleAcceptShare = async () => {
    if (!validation?.isValid || !user) return

    setAccepting(true)
    setError(null)

    try {
      const result = await petStorage.acceptShare(token)
      
      if (result.success) {
        setAccepted(true)
        // Redirect to main page after a short delay
        setTimeout(() => {
          router.push('/')
        }, 2000)
      } else {
        setError(result.error || 'Failed to accept share')
      }
    } catch (err) {
      console.error('Error accepting share:', err)
      setError('Failed to accept share')
    } finally {
      setAccepting(false)
    }
  }

  // Show loading state while checking auth or validating token
  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-4" />
            <p className="text-gray-600">Validating share link...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error state for invalid tokens
  if (error && !validation?.isValid) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-red-900">Invalid Share Link</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              className="w-full" 
              onClick={() => router.push('/')}
            >
              Go to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show success state after accepting
  if (accepted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-green-900">Share Accepted!</CardTitle>
            <CardDescription>
              You now have access to {validation?.pet?.name}. Redirecting to your dashboard...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  // Show login form if user is not authenticated
  if (!user && validation?.isValid) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="w-full max-w-md space-y-6">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Heart className="h-6 w-6 text-blue-600" />
              </div>
              <CardTitle>Pet Share Invitation</CardTitle>
              <CardDescription>
                {validation.owner?.name || 'Someone'} wants to share their pet "{validation.pet?.name}" with you
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-3">
                  <PawPrint className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-semibold text-blue-900">{validation.pet?.name}</h3>
                    <p className="text-sm text-blue-700">{validation.pet?.species}</p>
                  </div>
                </div>
              </div>
              <Alert className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Please sign in to accept this pet share invitation
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
          
          <LoginForm />
        </div>
      </div>
    )
  }

  // Show confirmation interface for authenticated users
  if (user && validation?.isValid) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Heart className="h-6 w-6 text-blue-600" />
            </div>
            <CardTitle>Accept Pet Share</CardTitle>
            <CardDescription>
              {validation.owner?.name || 'Someone'} wants to share their pet with you
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <PawPrint className="h-8 w-8 text-blue-600" />
                <div>
                  <h3 className="font-semibold text-blue-900">{validation.pet?.name}</h3>
                  <p className="text-sm text-blue-700">{validation.pet?.species}</p>
                  <Badge variant="secondary" className="mt-1">
                    Shared by {validation.owner?.name}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                <strong>{user.profile?.full_name || user.profile?.email}</strong> accepts <strong>{validation.pet?.name}</strong> as a shared <strong>{validation.pet?.species}</strong>
              </p>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex space-x-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => router.push('/')}
                disabled={accepting}
              >
                Cancel
              </Button>
              <Button
                className="flex-1"
                onClick={handleAcceptShare}
                disabled={accepting}
              >
                {accepting ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Accepting...
                  </>
                ) : (
                  'Confirm'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Fallback - should not reach here
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <AlertCircle className="h-8 w-8 text-red-600 mb-4" />
          <p className="text-gray-600">Something went wrong</p>
        </CardContent>
      </Card>
    </div>
  )
}
