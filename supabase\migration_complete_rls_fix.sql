-- Complete RLS Policy Fix - Alternative Approach
-- This completely rebuilds the RLS policies to eliminate recursion

-- Step 1: Disable <PERSON><PERSON> temporarily to avoid conflicts
ALTER TABLE pets DISABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop ALL existing policies for pets and pet_owners
DROP POLICY IF EXISTS "Users can view pets they own or have access to" ON pets;
DROP POLICY IF EXISTS "Users can create pets" ON pets;
DROP POLICY IF EXISTS "Pet creators and owners can update pets" ON pets;
DROP POLICY IF EXISTS "Pet creators can delete pets" ON pets;
DROP POLICY IF EXISTS "Users can view pet ownership for accessible pets" ON pet_owners;
DROP POLICY IF EXISTS "Pet creators can manage pet ownership" ON pet_owners;

-- Step 3: Create simple, non-recursive policies

-- Pets table policies (no references to pet_owners)
CREATE POLICY "pets_select_policy" ON pets
  FOR SELECT USING (
    created_by = auth.uid()
  );

CREATE POLICY "pets_insert_policy" ON pets
  FOR INSERT WITH CHECK (
    created_by = auth.uid()
  );

CREATE POLICY "pets_update_policy" ON pets
  FOR UPDATE USING (
    created_by = auth.uid()
  );

CREATE POLICY "pets_delete_policy" ON pets
  FOR DELETE USING (
    created_by = auth.uid()
  );

-- Pet_owners table policies (simple, direct checks only)
CREATE POLICY "pet_owners_select_policy" ON pet_owners
  FOR SELECT USING (
    user_id = auth.uid()
  );

CREATE POLICY "pet_owners_insert_policy" ON pet_owners
  FOR INSERT WITH CHECK (
    user_id = auth.uid()
  );

CREATE POLICY "pet_owners_update_policy" ON pet_owners
  FOR UPDATE USING (
    user_id = auth.uid()
  );

CREATE POLICY "pet_owners_delete_policy" ON pet_owners
  FOR DELETE USING (
    user_id = auth.uid()
  );

-- Step 4: Re-enable RLS
ALTER TABLE pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners ENABLE ROW LEVEL SECURITY;

-- Step 5: Test the policies
SELECT 'Policies created successfully' as status;
