-- Fix missing profiles and foreign key constraint issues
-- This migration ensures all auth users have corresponding profiles

-- Step 1: Create profiles for any auth.users that don't have them
INSERT INTO public.profiles (id, email, full_name, created_at, updated_at)
SELECT 
  au.id,
  au.email,
  COALESCE(au.raw_user_meta_data->>'full_name', 'User'),
  au.created_at,
  NOW()
FROM auth.users au
LEFT JOIN public.profiles p ON au.id = p.id
WHERE p.id IS NULL;

-- Step 2: Ensure the trigger function exists and is correct
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name, created_at, updated_at)
  VALUES (
    NEW.id, 
    NEW.email, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'User'),
    NEW.created_at,
    NOW()
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Recreate the trigger to ensure it works
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 4: Ensure the pet ownership trigger exists
CREATE OR REPLACE FUNCTION public.handle_new_pet()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.pet_owners (pet_id, user_id, role)
  VALUES (NEW.id, NEW.created_by, 'owner');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS on_pet_created ON pets;
CREATE TRIGGER on_pet_created
  AFTER INSERT ON pets
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_pet();

-- Step 5: Verify the fix
SELECT 
  'Profiles created/verified' as status,
  COUNT(*) as profile_count
FROM profiles;

SELECT 
  'Auth users count' as status,
  COUNT(*) as user_count
FROM auth.users;

-- Step 6: Check for any remaining mismatches
SELECT 
  'Users without profiles' as status,
  COUNT(*) as missing_profiles
FROM auth.users au
LEFT JOIN profiles p ON au.id = p.id
WHERE p.id IS NULL;
