import { supabase } from './supabase'
import { Tables, Inserts, Updates } from './supabase'

// Type definitions for our data models
export interface Pet extends Tables<'pets'> {}
export interface PetOwner extends Tables<'pet_owners'> {}
export interface FeedingLog extends Tables<'feeding_logs'> {}
export interface LitterLog extends Tables<'litter_logs'> {}
export interface Vaccination extends Tables<'vaccinations'> {}
export interface Note extends Tables<'notes'> {}
export interface PetShareToken extends Tables<'pet_share_tokens'> {}

// Extended interfaces
export interface PetShareTokenWithUrl extends PetShareToken {
  shareUrl: string
}

export interface ShareTokenValidation {
  isValid: boolean
  pet?: {
    id: string
    name: string
    species: string
  }
  owner?: {
    name: string
    email: string
  }
  tokenId?: string
  error?: string
}

// Extended PetOwner with profile information
export interface PetOwnerWithProfile extends PetOwner {
  profiles?: {
    email: string
    full_name: string | null
  }
}

export interface PetWithOwnership extends Pet {
  pet_owners: PetOwnerWithProfile[]
  is_owner: boolean
  is_creator: boolean
}

// Data access layer for Supabase operations
class SupabaseDataManager {
  // Helper to get current user ID
  private async getCurrentUserId(): Promise<string | null> {
    const { data: { user } } = await supabase.auth.getUser()
    return user?.id || null
  }

  // Pet operations
  async getPets(): Promise<PetWithOwnership[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    // RLS policies will automatically filter pets the user has access to
    const { data, error } = await supabase
      .from('pets')
      .select(`
        *,
        pet_owners (
          *,
          profiles (
            email,
            full_name
          )
        )
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching pets:', error)
      return []
    }

    return (data || []).map(pet => ({
      ...pet,
      is_owner: pet.pet_owners.some((po: PetOwnerWithProfile) => po.user_id === userId && po.role === 'owner'),
      is_creator: pet.created_by === userId
    }))
  }



  async createPet(petData: Omit<Inserts<'pets'>, 'created_by'>): Promise<Pet | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('pets')
      .insert({
        ...petData,
        created_by: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating pet:', error)
      return null
    }

    // The trigger will automatically create the owner relationship
    return data
  }

  async updatePet(petId: string, updates: Updates<'pets'>): Promise<Pet | null> {
    const { data, error } = await supabase
      .from('pets')
      .update(updates)
      .eq('id', petId)
      .select()
      .single()

    if (error) {
      console.error('Error updating pet:', error)
      return null
    }

    return data
  }

  async deletePet(petId: string): Promise<boolean> {
    const { error } = await supabase
      .from('pets')
      .delete()
      .eq('id', petId)

    if (error) {
      console.error('Error deleting pet:', error)
      return false
    }

    return true
  }

  // Pet sharing operations
  async sharePet(petId: string, userEmail: string, role: 'owner' | 'caretaker' = 'caretaker'): Promise<boolean> {
    // First, find the user by email
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', userEmail)
      .single()

    if (profileError || !profile) {
      console.error('User not found:', profileError)
      return false
    }

    const { error } = await supabase
      .from('pet_owners')
      .insert({
        pet_id: petId,
        user_id: profile.id,
        role
      })

    if (error) {
      console.error('Error sharing pet:', error)
      return false
    }

    return true
  }

  async removePetAccess(petId: string, userId: string): Promise<boolean> {
    const { error } = await supabase
      .from('pet_owners')
      .delete()
      .eq('pet_id', petId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error removing pet access:', error)
      return false
    }

    return true
  }

  // Share token operations
  async createShareToken(petId: string, expiresInDays: number = 30, maxUses?: number): Promise<PetShareTokenWithUrl | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    try {
      // Make API call to create share token
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return null

      const response = await fetch(`/api/pets/${petId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({ expiresInDays, maxUses })
      })

      if (!response.ok) {
        const error = await response.json()
        console.error('Error creating share token:', error)
        return null
      }

      const result = await response.json()

      // Return the token with URL
      return {
        id: '', // Will be set by the API
        token: result.token,
        pet_id: petId,
        created_by: userId,
        expires_at: result.expiresAt,
        is_active: true,
        usage_count: 0,
        max_uses: maxUses || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        shareUrl: result.shareUrl
      }
    } catch (error) {
      console.error('Error creating share token:', error)
      return null
    }
  }

  async getShareTokens(petId: string): Promise<PetShareTokenWithUrl[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return []

      const response = await fetch(`/api/pets/${petId}/share`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      })

      if (!response.ok) {
        console.error('Error fetching share tokens')
        return []
      }

      const result = await response.json()
      return result.shareTokens || []
    } catch (error) {
      console.error('Error fetching share tokens:', error)
      return []
    }
  }

  async deactivateShareToken(petId: string, tokenId: string): Promise<boolean> {
    const userId = await this.getCurrentUserId()
    if (!userId) return false

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return false

      const response = await fetch(`/api/pets/${petId}/share?tokenId=${tokenId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      })

      if (!response.ok) {
        console.error('Error deactivating share token')
        return false
      }

      return true
    } catch (error) {
      console.error('Error deactivating share token:', error)
      return false
    }
  }

  async validateShareToken(token: string): Promise<ShareTokenValidation> {
    try {
      const response = await fetch(`/api/share/${token}`, {
        method: 'GET'
      })

      if (!response.ok) {
        const error = await response.json()
        return {
          isValid: false,
          error: error.error || 'Invalid share link'
        }
      }

      const result = await response.json()
      return {
        isValid: result.isValid,
        pet: result.pet,
        owner: result.owner,
        tokenId: result.tokenId
      }
    } catch (error) {
      console.error('Error validating share token:', error)
      return {
        isValid: false,
        error: 'Failed to validate share link'
      }
    }
  }

  async acceptShare(token: string): Promise<{ success: boolean; message?: string; error?: string }> {
    const userId = await this.getCurrentUserId()
    if (!userId) return { success: false, error: 'Not authenticated' }

    try {
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return { success: false, error: 'Not authenticated' }

      const response = await fetch(`/api/share/${token}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      })

      const result = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: result.error || 'Failed to accept share'
        }
      }

      return {
        success: true,
        message: result.message
      }
    } catch (error) {
      console.error('Error accepting share:', error)
      return {
        success: false,
        error: 'Failed to accept share'
      }
    }
  }

  // Feeding log operations
  async getFeedingLogs(petId?: string): Promise<FeedingLog[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    let query = supabase
      .from('feeding_logs')
      .select('*')
      .order('timestamp', { ascending: false })

    if (petId) {
      query = query.eq('pet_id', petId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching feeding logs:', error)
      return []
    }

    return data || []
  }

  async addFeedingLog(log: Omit<Inserts<'feeding_logs'>, 'user_id'>): Promise<FeedingLog | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('feeding_logs')
      .insert({
        ...log,
        user_id: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding feeding log:', error)
      return null
    }

    return data
  }

  async removeFeedingLog(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('feeding_logs')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error removing feeding log:', error)
      return false
    }

    return true
  }

  // Litter log operations
  async getLitterLogs(petId?: string): Promise<LitterLog[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    let query = supabase
      .from('litter_logs')
      .select('*')
      .order('timestamp', { ascending: false })

    if (petId) {
      query = query.eq('pet_id', petId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching litter logs:', error)
      return []
    }

    return data || []
  }

  async addLitterLog(log: Omit<Inserts<'litter_logs'>, 'user_id'>): Promise<LitterLog | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('litter_logs')
      .insert({
        ...log,
        user_id: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding litter log:', error)
      return null
    }

    return data
  }

  async removeLitterLog(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('litter_logs')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error removing litter log:', error)
      return false
    }

    return true
  }

  // Vaccination operations
  async getVaccinations(petId?: string): Promise<Vaccination[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    let query = supabase
      .from('vaccinations')
      .select('*')
      .order('date', { ascending: false })

    if (petId) {
      query = query.eq('pet_id', petId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching vaccinations:', error)
      return []
    }

    return data || []
  }

  async addVaccination(vaccination: Omit<Inserts<'vaccinations'>, 'user_id'>): Promise<Vaccination | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('vaccinations')
      .insert({
        ...vaccination,
        user_id: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding vaccination:', error)
      return null
    }

    return data
  }

  async removeVaccination(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('vaccinations')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error removing vaccination:', error)
      return false
    }

    return true
  }

  // Notes operations
  async getNotes(petId?: string): Promise<Note[]> {
    const userId = await this.getCurrentUserId()
    if (!userId) return []

    let query = supabase
      .from('notes')
      .select('*')
      .order('timestamp', { ascending: false })

    if (petId) {
      query = query.eq('pet_id', petId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching notes:', error)
      return []
    }

    return data || []
  }

  async addNote(note: Omit<Inserts<'notes'>, 'user_id'>): Promise<Note | null> {
    const userId = await this.getCurrentUserId()
    if (!userId) return null

    const { data, error } = await supabase
      .from('notes')
      .insert({
        ...note,
        user_id: userId
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding note:', error)
      return null
    }

    return data
  }

  async removeNote(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('notes')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error removing note:', error)
      return false
    }

    return true
  }
}

// Create singleton instance
export const storage = new SupabaseDataManager();

// New Supabase-based storage interface
export const petStorage = {
  // Pet operations
  getPets: () => storage.getPets(),
  createPet: (petData: Omit<Inserts<'pets'>, 'created_by'>) => storage.createPet(petData),
  updatePet: (petId: string, updates: Updates<'pets'>) => storage.updatePet(petId, updates),
  deletePet: (petId: string) => storage.deletePet(petId),
  sharePet: (petId: string, userEmail: string, role?: 'owner' | 'caretaker') => storage.sharePet(petId, userEmail, role),
  removePetAccess: (petId: string, userId: string) => storage.removePetAccess(petId, userId),

  // Share tokens
  createShareToken: (petId: string, expiresInDays?: number, maxUses?: number) => storage.createShareToken(petId, expiresInDays, maxUses),
  getShareTokens: (petId: string) => storage.getShareTokens(petId),
  deactivateShareToken: (petId: string, tokenId: string) => storage.deactivateShareToken(petId, tokenId),
  validateShareToken: (token: string) => storage.validateShareToken(token),
  acceptShare: (token: string) => storage.acceptShare(token),

  // Feeding logs
  getFeedingLogs: (petId?: string) => storage.getFeedingLogs(petId),
  addFeedingLog: (log: Omit<Inserts<'feeding_logs'>, 'user_id'>) => storage.addFeedingLog(log),
  removeFeedingLog: (id: string) => storage.removeFeedingLog(id),

  // Litter logs
  getLitterLogs: (petId?: string) => storage.getLitterLogs(petId),
  addLitterLog: (log: Omit<Inserts<'litter_logs'>, 'user_id'>) => storage.addLitterLog(log),
  removeLitterLog: (id: string) => storage.removeLitterLog(id),

  // Vaccinations
  getVaccinations: (petId?: string) => storage.getVaccinations(petId),
  addVaccination: (vaccination: Omit<Inserts<'vaccinations'>, 'user_id'>) => storage.addVaccination(vaccination),
  removeVaccination: (id: string) => storage.removeVaccination(id),

  // Notes
  getNotes: (petId?: string) => storage.getNotes(petId),
  addNote: (note: Omit<Inserts<'notes'>, 'user_id'>) => storage.addNote(note),
  removeNote: (id: string) => storage.removeNote(id),
};

// React hook for listening to storage changes
export function useStorageListener(callback: (key: string) => void) {
  if (typeof window !== 'undefined') {
    const handleStorageChange = (event: CustomEvent) => {
      callback(event.detail.key);
    };

    window.addEventListener('pet-data-updated', handleStorageChange as EventListener);
    
    return () => {
      window.removeEventListener('pet-data-updated', handleStorageChange as EventListener);
    };
  }
  return () => {};
}