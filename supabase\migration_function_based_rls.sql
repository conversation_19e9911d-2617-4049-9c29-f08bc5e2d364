-- Function-based RLS approach to eliminate recursion
-- This uses security definer functions to break the recursion cycle

-- Step 1: Disable <PERSON><PERSON> temporarily
ALTER TABLE pets DISABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners DISABLE ROW LEVEL SECURITY;
ALTER TABLE feeding_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE litter_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE vaccinations DISABLE ROW LEVEL SECURITY;
ALTER TABLE notes DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop all existing policies
DROP POLICY IF EXISTS "Users can view pets they own or have access to" ON pets;
DROP POLICY IF EXISTS "Users can create pets" ON pets;
DROP POLICY IF EXISTS "Pet creators and owners can update pets" ON pets;
DROP POLICY IF EXISTS "Pet creators can delete pets" ON pets;
DROP POLICY IF EXISTS "Users can view pet ownership for accessible pets" ON pet_owners;
DROP POLICY IF EXISTS "Pet creators can manage pet ownership" ON pet_owners;

-- Drop policies for other tables too
DROP POLICY IF EXISTS "Users can view feeding logs for accessible pets" ON feeding_logs;
DROP POLICY IF EXISTS "Users can create feeding logs for accessible pets" ON feeding_logs;
DROP POLICY IF EXISTS "Users can update own feeding logs" ON feeding_logs;
DROP POLICY IF EXISTS "Users can delete own feeding logs" ON feeding_logs;

DROP POLICY IF EXISTS "Users can view litter logs for accessible pets" ON litter_logs;
DROP POLICY IF EXISTS "Users can create litter logs for accessible pets" ON litter_logs;
DROP POLICY IF EXISTS "Users can update own litter logs" ON litter_logs;
DROP POLICY IF EXISTS "Users can delete own litter logs" ON litter_logs;

DROP POLICY IF EXISTS "Users can view vaccinations for accessible pets" ON vaccinations;
DROP POLICY IF EXISTS "Users can create vaccinations for accessible pets" ON vaccinations;
DROP POLICY IF EXISTS "Users can update own vaccinations" ON vaccinations;
DROP POLICY IF EXISTS "Users can delete own vaccinations" ON vaccinations;

DROP POLICY IF EXISTS "Users can view notes for accessible pets" ON notes;
DROP POLICY IF EXISTS "Users can create notes for accessible pets" ON notes;
DROP POLICY IF EXISTS "Users can update own notes" ON notes;
DROP POLICY IF EXISTS "Users can delete own notes" ON notes;

-- Step 3: Create helper function to check pet access without recursion
CREATE OR REPLACE FUNCTION public.user_has_pet_access(pet_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
  has_access BOOLEAN := FALSE;
BEGIN
  -- Check if user created the pet
  SELECT EXISTS(
    SELECT 1 FROM pets 
    WHERE id = pet_id_param AND created_by = user_id_param
  ) INTO has_access;
  
  -- If not creator, check if user is in pet_owners (with RLS disabled context)
  IF NOT has_access THEN
    SELECT EXISTS(
      SELECT 1 FROM pet_owners 
      WHERE pet_id = pet_id_param AND user_id = user_id_param
    ) INTO has_access;
  END IF;
  
  RETURN has_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Create simple, non-recursive policies using the function

-- Pets policies
CREATE POLICY "pets_select_policy" ON pets
  FOR SELECT USING (
    created_by = auth.uid()
  );

CREATE POLICY "pets_insert_policy" ON pets
  FOR INSERT WITH CHECK (
    created_by = auth.uid()
  );

CREATE POLICY "pets_update_policy" ON pets
  FOR UPDATE USING (
    created_by = auth.uid()
  );

CREATE POLICY "pets_delete_policy" ON pets
  FOR DELETE USING (
    created_by = auth.uid()
  );

-- Pet_owners policies (simple)
CREATE POLICY "pet_owners_all_policy" ON pet_owners
  FOR ALL USING (
    user_id = auth.uid()
  );

-- Step 5: Re-enable RLS
ALTER TABLE pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners ENABLE ROW LEVEL SECURITY;
ALTER TABLE feeding_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE litter_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE vaccinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;

-- Step 6: Create policies for other tables using direct checks
CREATE POLICY "feeding_logs_policy" ON feeding_logs
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "litter_logs_policy" ON litter_logs
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "vaccinations_policy" ON vaccinations
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "notes_policy" ON notes
  FOR ALL USING (user_id = auth.uid());

SELECT 'RLS policies recreated successfully' as status;
