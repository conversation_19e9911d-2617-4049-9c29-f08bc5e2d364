'use client';

import { useState, useEffect } from 'react';
import { petStorage } from '@/lib/storage';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Syringe, Plus, Calendar, Clock, Trash2, AlertTriangle, CheckCircle } from 'lucide-react';

interface VaccinationTrackerProps {
  petId: string;
  onUpdate?: () => void;
}

export function VaccinationTracker({ petId, onUpdate }: VaccinationTrackerProps) {
  const { toast } = useToast();
  const [vaccinations, setVaccinations] = useState<any[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newVaccination, setNewVaccination] = useState({
    vaccine: '',
    date: '',
    next_due: '',
    veterinarian: '',
    lot_number: '',
    notes: ''
  });

  useEffect(() => {
    loadVaccinations();
  }, [petId]);

  const loadVaccinations = async () => {
    setLoading(true);
    try {
      const vaccinationData = await petStorage.getVaccinations(petId);
      setVaccinations(vaccinationData);
    } catch (error) {
      console.error('Error loading vaccinations:', error);
      toast({
        title: "Error",
        description: "Failed to load vaccinations",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async () => {
    if (!newVaccination.vaccine || !newVaccination.date || !newVaccination.veterinarian) {
      toast({
        title: "Error",
        description: "Please enter vaccine name, date, and veterinarian",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const vaccination = await petStorage.addVaccination({
        pet_id: petId,
        vaccine: newVaccination.vaccine,
        date: newVaccination.date,
        next_due: newVaccination.next_due || null,
        veterinarian: newVaccination.veterinarian,
        lot_number: newVaccination.lot_number || null,
        notes: newVaccination.notes || null
      });

      if (vaccination) {
        await loadVaccinations();
        onUpdate?.();
        setNewVaccination({
          vaccine: '',
          date: '',
          next_due: '',
          veterinarian: '',
          lot_number: '',
          notes: ''
        });
        setIsDialogOpen(false);

        toast({
          title: "Vaccination recorded!",
          description: `Added ${vaccination.vaccine} vaccination.`,
        });
      }
    } catch (error) {
      console.error('Error adding vaccination:', error);
      toast({
        title: "Error",
        description: "Failed to add vaccination",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    setLoading(true);
    try {
      const success = await petStorage.removeVaccination(id);
      if (success) {
        await loadVaccinations();
        onUpdate?.();
        toast({
          title: "Success",
          description: "Vaccination deleted",
        });
      }
    } catch (error) {
      console.error('Error deleting vaccination:', error);
      toast({
        title: "Error",
        description: "Failed to delete vaccination",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  const isDueSoon = (dueDate: string) => {
    const due = new Date(dueDate);
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);
    
    return due > today && due <= thirtyDaysFromNow;
  };

  const getUpcomingVaccinations = () => {
    return vaccinations
      .filter(v => v.nextDue)
      .sort((a, b) => new Date(a.nextDue!).getTime() - new Date(b.nextDue!).getTime());
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Vaccination Tracker</h2>
          <p className="text-gray-600">Monitor your pet&apos;s vaccination history and upcoming shots</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-green-600 hover:bg-green-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Vaccination
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Record New Vaccination</DialogTitle>
              <DialogDescription>
                Add details about a vaccination
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="vaccine">Vaccine Type *</Label>
                <Select value={newVaccination.vaccine} onValueChange={(value) => setNewVaccination({ ...newVaccination, vaccine: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select vaccine type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rabies">Rabies</SelectItem>
                    <SelectItem value="dhpp">DHPP (Distemper, Hepatitis, Parvovirus, Parainfluenza)</SelectItem>
                    <SelectItem value="fvrcp">FVRCP (Feline Viral Rhinotracheitis, Calicivirus, Panleukopenia)</SelectItem>
                    <SelectItem value="bordetella">Bordetella</SelectItem>
                    <SelectItem value="lyme">Lyme Disease</SelectItem>
                    <SelectItem value="leukemia">Feline Leukemia</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="date">Date Given *</Label>
                <Input
                  id="date"
                  type="date"
                  value={newVaccination.date}
                  onChange={(e) => setNewVaccination({ ...newVaccination, date: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="nextDue">Next Due Date</Label>
                <Input
                  id="nextDue"
                  type="date"
                  value={newVaccination.next_due}
                  onChange={(e) => setNewVaccination({ ...newVaccination, next_due: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="veterinarian">Veterinarian/Clinic *</Label>
                <Input
                  id="veterinarian"
                  value={newVaccination.veterinarian}
                  onChange={(e) => setNewVaccination({ ...newVaccination, veterinarian: e.target.value })}
                  placeholder="Dr. Smith's Animal Hospital"
                />
              </div>
              <div>
                <Label htmlFor="lotNumber">Lot Number</Label>
                <Input
                  id="lotNumber"
                  value={newVaccination.lot_number}
                  onChange={(e) => setNewVaccination({ ...newVaccination, lot_number: e.target.value })}
                  placeholder="Vaccine lot number (optional)"
                />
              </div>
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={newVaccination.notes}
                  onChange={(e) => setNewVaccination({ ...newVaccination, notes: e.target.value })}
                  placeholder="Any reactions, observations, or additional notes..."
                  className="min-h-[60px]"
                />
              </div>
              <div className="flex space-x-2 pt-4">
                <Button onClick={handleAdd} className="flex-1" disabled={!newVaccination.vaccine || !newVaccination.date || !newVaccination.veterinarian || loading}>
                  Add Vaccination
                </Button>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Upcoming Vaccinations Alert */}
      {getUpcomingVaccinations().filter(v => v.nextDue && (isOverdue(v.nextDue) || isDueSoon(v.nextDue))).length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              <span>Vaccination Reminders</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {getUpcomingVaccinations()
                .filter(v => v.next_due && (isOverdue(v.next_due) || isDueSoon(v.next_due)))
                .map((vaccination) => (
                  <div key={vaccination.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                    <div>
                      <p className="font-medium text-gray-900">{vaccination.vaccine}</p>
                      <p className="text-sm text-gray-600">Due: {formatDate(vaccination.next_due!)}</p>
                    </div>
                    <Badge variant={isOverdue(vaccination.next_due!) ? "destructive" : "secondary"}>
                      {isOverdue(vaccination.next_due!) ? 'Overdue' : 'Due Soon'}
                    </Badge>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-green-100 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Vaccinations</p>
                <p className="text-2xl font-bold text-gray-900">{vaccinations.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-100 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">This Year</p>
                <p className="text-2xl font-bold text-gray-900">
                  {vaccinations.filter(v => new Date(v.date).getFullYear() === new Date().getFullYear()).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-orange-100 p-3 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Upcoming</p>
                <p className="text-2xl font-bold text-gray-900">
                  {getUpcomingVaccinations().filter(v => v.nextDue && !isOverdue(v.nextDue!)).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Vaccination History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Syringe className="h-5 w-5" />
            <span>Vaccination History</span>
          </CardTitle>
          <CardDescription>
            Complete record of all vaccinations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {vaccinations.length === 0 ? (
            <div className="text-center py-8">
              <Syringe className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <p className="text-gray-500">No vaccinations recorded yet</p>
              <p className="text-sm text-gray-400">Add your pet&apos;s vaccination records to get started</p>
            </div>
          ) : (
            <div className="space-y-4">
              {vaccinations.map((vaccination) => (
                <div key={vaccination.id} className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                        {vaccination.vaccine}
                      </Badge>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(vaccination.date)}</span>
                      </div>
                      {vaccination.next_due && (
                        <Badge variant={isOverdue(vaccination.next_due) ? "destructive" : isDueSoon(vaccination.next_due) ? "secondary" : "outline"}>
                          Next: {formatDate(vaccination.next_due)}
                        </Badge>
                      )}
                    </div>
                    {vaccination.veterinarian && (
                      <p className="text-sm font-medium text-gray-900 mb-1">Veterinarian: {vaccination.veterinarian}</p>
                    )}
                    {vaccination.lot_number && (
                      <p className="text-sm text-gray-600 mb-1">Lot Number: {vaccination.lot_number}</p>
                    )}
                    {vaccination.notes && (
                      <p className="text-sm text-gray-600">{vaccination.notes}</p>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(vaccination.id)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}