# Pet Image Storage Setup

This document explains how to set up image storage for pet profiles in the FurFormance Pet Tracker.

## Storage Bucket Setup

To enable pet image uploads, you need to create a storage bucket in your Supabase project:

### Option 1: Using Supabase Dashboard

1. Go to your Supabase project dashboard
2. Navigate to **Storage** in the left sidebar
3. Click **Create a new bucket**
4. Set the following configuration:
   - **Bucket name**: `pet-images`
   - **Public bucket**: ✅ Enabled
   - **File size limit**: `5242880` (5MB)
   - **Allowed MIME types**: `image/jpeg,image/jpg,image/png,image/webp`

### Option 2: Using SQL Migration

Run the SQL migration file in your Supabase SQL Editor:

```sql
-- Copy and paste the contents of supabase/migration_setup_storage.sql
```

## Features Implemented

### Pet Profile Editor
- **Location**: `components/pet-profile-editor.tsx`
- **Features**:
  - Edit all pet information (name, nickname, species, breed, date of birth, weight, color, notes)
  - Upload and manage pet images
  - Image preview and removal
  - Automatic image resizing (800x600 max)
  - File validation (JPEG, PNG, WebP, max 5MB)
  - Real-time character count validation
  - Field-specific error messages
  - Save operation timeout protection (30 seconds)

### Pet Avatar Component
- **Location**: `components/pet-avatar.tsx`
- **Features**:
  - Displays pet images throughout the app
  - Falls back to PawPrint icon when no image is available
  - Multiple size options (sm, md, lg, xl)
  - Responsive design

### Image Upload Manager
- **Location**: `lib/image-upload.ts`
- **Features**:
  - Handles file uploads to Supabase Storage
  - Image validation and resizing
  - Automatic cleanup of old images
  - Error handling and user feedback

### Validation System
- **Location**: `lib/validation.ts`
- **Features**:
  - Character limits for all pet fields
  - Real-time validation feedback
  - Field-specific error messages
  - Character count tracking

## Character Limits

The following character limits are enforced for pet information:

- **Name**: 50 characters (required)
- **Nickname**: 100 characters (optional)
- **Species**: 30 characters (required)
- **Breed**: 50 characters (optional)
- **Weight**: 20 characters (optional)
- **Color**: 30 characters (optional)
- **Notes**: 1000 characters (optional)

These limits prevent database issues and ensure a good user experience. Real-time character counts are displayed next to each field, and validation errors appear immediately when limits are exceeded.

## Usage

### Editing Pet Profiles

1. Navigate to the **Pets** tab
2. Find your pet in the list
3. Click the **Edit** button (pencil icon)
4. Update any information as needed:
   - **Name**: Required field, up to 50 characters
   - **Nickname**: Optional field, up to 100 characters for fun/affectionate names
   - **Species**: Required field, up to 30 characters
   - **Breed, Weight, Color**: Optional fields with character limits
   - **Notes**: Optional field, up to 1000 characters for detailed information
5. To add/change a photo:
   - Click **Upload Photo** or **Change Photo**
   - Select an image file (JPEG, PNG, or WebP, max 5MB)
   - The image will be automatically resized and uploaded
6. Watch the character counts to stay within limits
7. Click **Save Changes** (operation will timeout after 30 seconds if there are issues)

### Pet Images in the Interface

- **Header**: When a pet is selected, their image appears in the main header instead of the default PawPrint icon
- **Pet Cards**: Each pet card shows their image alongside their information
- **Activity Feed**: Pet images appear throughout the interface when that pet is selected

## File Structure

```
components/
├── pet-profile-editor.tsx    # Main editing dialog component
├── pet-avatar.tsx           # Reusable pet image component
└── pet-manager.tsx          # Updated with edit functionality

lib/
└── image-upload.ts          # Image upload utilities

supabase/
└── migration_setup_storage.sql  # Storage bucket setup
```

## Security

The storage bucket is configured with Row Level Security (RLS) policies:

- Users can only upload images to their own folder (`user_id/pet_id/`)
- All users can view pet images (public bucket)
- Users can only update/delete their own uploaded images
- File size and type restrictions are enforced

## Troubleshooting

### Images Not Uploading
1. Verify the storage bucket exists and is public
2. Check that RLS policies are properly configured
3. Ensure file meets size and type requirements
4. Check browser console for error messages

### Images Not Displaying
1. Verify the image URL is correctly stored in the database
2. Check that the storage bucket is public
3. Ensure the image file still exists in storage

### Permission Errors
1. Verify RLS policies are correctly configured
2. Check that the user is authenticated
3. Ensure the user owns the pet they're trying to edit

### Save Operation Issues
1. **"Forever saving" button**: This indicates a validation error or timeout
   - Check for red error messages below form fields
   - Ensure all required fields (name, species) are filled
   - Verify character limits are not exceeded
   - Wait for the 30-second timeout, then try again
2. **Validation errors**: Red borders and error messages indicate field issues
   - Reduce text length to stay within character limits
   - Fill in required fields (name and species)
3. **Timeout errors**: If save takes longer than 30 seconds
   - Check your internet connection
   - Try again with a shorter pet name or description
   - Contact support if the issue persists
