'use client'

import { useState, useRef, useEffect } from 'react'
import { petStorage, Pet } from '@/lib/storage'
import { ImageUploadManager } from '@/lib/image-upload'
import { validatePetData, PET_FIELD_LIMITS, getCharacterCount, getRemainingCharacters } from '@/lib/validation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useToast } from '@/hooks/use-toast'
import { Camera, Edit, Loader2, X, Upload } from 'lucide-react'
import Image from 'next/image'

interface PetProfileEditorProps {
  pet: Pet
  onUpdate: () => void
  trigger?: React.ReactNode
}

export function PetProfileEditor({ pet, onUpdate, trigger }: PetProfileEditorProps) {
  const { toast } = useToast()
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [imageUploading, setImageUploading] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [formData, setFormData] = useState({
    name: pet.name,
    nickname: pet.nickname || '',
    species: pet.species,
    breed: pet.breed || '',
    date_of_birth: pet.date_of_birth || '',
    weight: pet.weight || '',
    color: pet.color || '',
    notes: pet.notes || '',
    image_url: pet.image_url || ''
  })

  // Timeout for save operation
  const SAVE_TIMEOUT = 30000 // 30 seconds

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear validation error for this field when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setImageUploading(true)
    try {
      // Resize image before upload
      const resizedFile = await ImageUploadManager.resizeImage(file, 800, 600)
      
      // Upload to Supabase Storage
      const result = await ImageUploadManager.uploadPetImage(resizedFile, pet.id)
      
      if (result.error) {
        toast({
          title: "Upload Failed",
          description: result.error,
          variant: "destructive"
        })
        return
      }

      if (result.url) {
        // Delete old image if it exists
        if (formData.image_url) {
          await ImageUploadManager.deletePetImage(formData.image_url)
        }
        
        setFormData(prev => ({ ...prev, image_url: result.url! }))
        toast({
          title: "Image Uploaded",
          description: "Pet image has been updated successfully"
        })
      }
    } catch (error) {
      console.error('Image upload error:', error)
      toast({
        title: "Upload Failed",
        description: "Failed to upload image",
        variant: "destructive"
      })
    } finally {
      setImageUploading(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemoveImage = async () => {
    if (!formData.image_url) return

    setImageUploading(true)
    try {
      await ImageUploadManager.deletePetImage(formData.image_url)
      setFormData(prev => ({ ...prev, image_url: '' }))
      toast({
        title: "Image Removed",
        description: "Pet image has been removed"
      })
    } catch (error) {
      console.error('Image removal error:', error)
      toast({
        title: "Error",
        description: "Failed to remove image",
        variant: "destructive"
      })
    } finally {
      setImageUploading(false)
    }
  }

  const handleSave = async () => {
    // Validate form data
    const validation = validatePetData({
      name: formData.name,
      nickname: formData.nickname,
      species: formData.species,
      breed: formData.breed,
      weight: formData.weight,
      color: formData.color,
      notes: formData.notes
    })

    if (!validation.isValid) {
      const errorMap: {[key: string]: string} = {}
      validation.errors.forEach(error => {
        errorMap[error.field] = error.message
      })
      setValidationErrors(errorMap)

      toast({
        title: "Validation Error",
        description: "Please fix the errors below",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    setValidationErrors({})

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Save operation timed out')), SAVE_TIMEOUT)
    })

    try {
      const updates = {
        name: formData.name.trim(),
        nickname: formData.nickname.trim() || null,
        species: formData.species.trim(),
        breed: formData.breed.trim() || null,
        date_of_birth: formData.date_of_birth || null,
        weight: formData.weight.trim() || null,
        color: formData.color.trim() || null,
        notes: formData.notes.trim() || null,
        image_url: formData.image_url || null
      }

      // Race between the update operation and timeout
      const updatedPet = await Promise.race([
        petStorage.updatePet(pet.id, updates),
        timeoutPromise
      ]) as Pet | null

      if (updatedPet) {
        onUpdate()
        setIsOpen(false)
        toast({
          title: "Profile Updated",
          description: `${updatedPet.name}'s profile has been updated successfully`
        })
      } else {
        toast({
          title: "Update Failed",
          description: "Failed to update pet profile",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error updating pet:', error)
      const errorMessage = error instanceof Error && error.message === 'Save operation timed out'
        ? "The save operation timed out. Please try again."
        : "Failed to update pet profile"

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    // Reset form data to original values
    setFormData({
      name: pet.name,
      nickname: pet.nickname || '',
      species: pet.species,
      breed: pet.breed || '',
      date_of_birth: pet.date_of_birth || '',
      weight: pet.weight || '',
      color: pet.color || '',
      notes: pet.notes || '',
      image_url: pet.image_url || ''
    })
    setValidationErrors({})
    setIsOpen(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit Profile
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit {pet.name}&apos;s Profile</DialogTitle>
          <DialogDescription>
            Update your pet&apos;s information and photo
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Image Upload Section */}
          <div className="space-y-4">
            <Label>Pet Photo</Label>
            <div className="flex items-center space-x-4">
              <div className="relative">
                {formData.image_url ? (
                  <div className="relative w-24 h-24 rounded-lg overflow-hidden">
                    <Image
                      src={formData.image_url}
                      alt={formData.name}
                      fill
                      className="object-cover"
                    />
                    <button
                      onClick={handleRemoveImage}
                      disabled={imageUploading}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 disabled:opacity-50"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ) : (
                  <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Camera className="h-8 w-8 text-gray-400" />
                  </div>
                )}
              </div>
              
              <div className="space-y-2">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={imageUploading}
                >
                  {imageUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      {formData.image_url ? 'Change Photo' : 'Upload Photo'}
                    </>
                  )}
                </Button>
                <p className="text-xs text-gray-500">
                  JPEG, PNG, or WebP. Max 5MB.
                </p>
              </div>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="edit-name">
                Name * ({getCharacterCount(formData.name)}/{PET_FIELD_LIMITS.name})
              </Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Pet's name"
                className={validationErrors.name ? 'border-red-500' : ''}
              />
              {validationErrors.name && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.name}</p>
              )}
            </div>
            <div>
              <Label htmlFor="edit-species">
                Species * ({getCharacterCount(formData.species)}/{PET_FIELD_LIMITS.species})
              </Label>
              <Input
                id="edit-species"
                value={formData.species}
                onChange={(e) => handleInputChange('species', e.target.value)}
                placeholder="Dog, Cat, etc."
                className={validationErrors.species ? 'border-red-500' : ''}
              />
              {validationErrors.species && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.species}</p>
              )}
            </div>
          </div>

          {/* Nickname */}
          <div>
            <Label htmlFor="edit-nickname">
              Nickname ({getCharacterCount(formData.nickname)}/{PET_FIELD_LIMITS.nickname})
            </Label>
            <Input
              id="edit-nickname"
              value={formData.nickname}
              onChange={(e) => handleInputChange('nickname', e.target.value)}
              placeholder="Pet's nickname (optional)"
              className={validationErrors.nickname ? 'border-red-500' : ''}
            />
            {validationErrors.nickname && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.nickname}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              A fun or affectionate name for your pet
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="edit-breed">
                Breed ({getCharacterCount(formData.breed)}/{PET_FIELD_LIMITS.breed})
              </Label>
              <Input
                id="edit-breed"
                value={formData.breed}
                onChange={(e) => handleInputChange('breed', e.target.value)}
                placeholder="Breed (optional)"
                className={validationErrors.breed ? 'border-red-500' : ''}
              />
              {validationErrors.breed && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.breed}</p>
              )}
            </div>
            <div>
              <Label htmlFor="edit-dob">Date of Birth</Label>
              <Input
                id="edit-dob"
                type="date"
                value={formData.date_of_birth}
                onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="edit-weight">
                Weight ({getCharacterCount(formData.weight)}/{PET_FIELD_LIMITS.weight})
              </Label>
              <Input
                id="edit-weight"
                value={formData.weight}
                onChange={(e) => handleInputChange('weight', e.target.value)}
                placeholder="e.g., 15 lbs, 7 kg"
                className={validationErrors.weight ? 'border-red-500' : ''}
              />
              {validationErrors.weight && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.weight}</p>
              )}
            </div>
            <div>
              <Label htmlFor="edit-color">
                Color ({getCharacterCount(formData.color)}/{PET_FIELD_LIMITS.color})
              </Label>
              <Input
                id="edit-color"
                value={formData.color}
                onChange={(e) => handleInputChange('color', e.target.value)}
                placeholder="Pet's color"
                className={validationErrors.color ? 'border-red-500' : ''}
              />
              {validationErrors.color && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.color}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="edit-notes">
              Notes ({getCharacterCount(formData.notes)}/{PET_FIELD_LIMITS.notes})
            </Label>
            <Textarea
              id="edit-notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes about your pet..."
              rows={3}
              className={validationErrors.notes ? 'border-red-500' : ''}
            />
            {validationErrors.notes && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.notes}</p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={handleCancel} disabled={loading}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
