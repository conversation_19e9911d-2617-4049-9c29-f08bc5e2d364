'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cacheUtils } from '@/lib/cache-utils'
import { RefreshCw, Trash2, Bug, AlertTriangle } from 'lucide-react'

interface LoadingDebugProps {
  loading: boolean
  user: any
  onRetry?: () => void
}

export function LoadingDebug({ loading, user, onRetry }: LoadingDebugProps) {
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const [loadingTime, setLoadingTime] = useState(0)
  const [showDebug, setShowDebug] = useState(false)

  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (loading) {
      const startTime = Date.now()
      interval = setInterval(() => {
        setLoadingTime(Date.now() - startTime)
      }, 100)
    } else {
      setLoadingTime(0)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [loading])

  useEffect(() => {
    // Show debug panel if loading takes too long
    if (loadingTime > 5000) {
      setShowDebug(true)
    }
  }, [loadingTime])

  const handleClearCache = async () => {
    await cacheUtils.clearAllCache()
    window.location.reload()
  }

  const handleClearSupabaseCache = async () => {
    cacheUtils.clearSupabaseCache()
    if (onRetry) onRetry()
  }

  const handleGetDebugInfo = () => {
    const info = cacheUtils.getCacheInfo()
    setDebugInfo(info)
    cacheUtils.debugCache()
  }

  // Only show if loading is taking too long or debug is manually enabled
  if (!showDebug && loadingTime < 5000) {
    return null
  }

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <Card className="border-orange-200 bg-orange-50">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center space-x-2 text-orange-800">
            <AlertTriangle className="h-5 w-5" />
            <span>Loading Issue Detected</span>
          </CardTitle>
          <CardDescription>
            The app has been loading for {formatTime(loadingTime)}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Status:</span>
            <Badge variant={loading ? "destructive" : "default"}>
              {loading ? "Loading..." : user ? "Authenticated" : "Not Authenticated"}
            </Badge>
          </div>

          <div className="space-y-2">
            <Button 
              onClick={handleClearSupabaseCache}
              variant="outline" 
              size="sm" 
              className="w-full"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Clear Auth Cache
            </Button>
            
            <Button 
              onClick={handleClearCache}
              variant="outline" 
              size="sm" 
              className="w-full"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All Cache & Reload
            </Button>
            
            <Button 
              onClick={handleGetDebugInfo}
              variant="outline" 
              size="sm" 
              className="w-full"
            >
              <Bug className="h-4 w-4 mr-2" />
              Debug Info
            </Button>
          </div>

          {debugInfo && (
            <div className="mt-3 p-2 bg-white rounded border text-xs">
              <div className="font-medium mb-1">Cache Status:</div>
              <div>localStorage: {debugInfo.localStorage.itemCount} items</div>
              <div>Supabase keys: {debugInfo.localStorage.supabaseKeys.length}</div>
              <div>IndexedDB: {debugInfo.indexedDB.available ? "Available" : "Not available"}</div>
            </div>
          )}

          <div className="text-xs text-orange-600 mt-2">
            If the app won&apos;t load, try clearing the cache or refreshing the page.
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Hook for easy integration
export function useLoadingDebug(loading: boolean, user: any) {
  const [showDebug, setShowDebug] = useState(false)
  const [loadingTime, setLoadingTime] = useState(0)

  useEffect(() => {
    let startTime: number
    let interval: NodeJS.Timeout

    if (loading) {
      startTime = Date.now()
      interval = setInterval(() => {
        const elapsed = Date.now() - startTime
        setLoadingTime(elapsed)
        
        // Auto-show debug after 5 seconds
        if (elapsed > 5000) {
          setShowDebug(true)
        }
      }, 100)
    } else {
      setLoadingTime(0)
      setShowDebug(false)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [loading])

  return {
    showDebug,
    loadingTime,
    setShowDebug
  }
}
