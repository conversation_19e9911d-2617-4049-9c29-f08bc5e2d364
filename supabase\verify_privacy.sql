-- Script to verify that pet privacy controls are working correctly
-- Run this in your Supabase SQL Editor to check the current state

-- Check 1: Verify RLS is enabled on all tables
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'pets', 'pet_owners', 'feeding_logs', 'litter_logs', 'vaccinations', 'notes')
ORDER BY tablename;

-- Check 2: List all current policies on the pets table
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' AND tablename = 'pets'
ORDER BY policyname;

-- Check 3: Verify that pets have proper ownership records
SELECT 
  p.name as pet_name,
  p.species,
  p.created_by,
  po.user_id as owner_id,
  po.role,
  prof.email as owner_email,
  prof.full_name as owner_name
FROM pets p
LEFT JOIN pet_owners po ON p.id = po.pet_id AND p.created_by = po.user_id
LEFT JOIN profiles prof ON po.user_id = prof.id
ORDER BY p.created_at DESC;

-- Check 4: Look for any pets without ownership records (these would be problematic)
SELECT 
  p.id,
  p.name,
  p.species,
  p.created_by,
  'Missing ownership record' as issue
FROM pets p
LEFT JOIN pet_owners po ON p.id = po.pet_id AND p.created_by = po.user_id
WHERE po.id IS NULL;

-- Check 5: Verify the pet creation trigger exists
SELECT 
  trigger_name,
  event_manipulation,
  action_timing,
  action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'pets' 
  AND trigger_name = 'on_pet_created';

-- Check 6: Count pets and their sharing status
SELECT 
  'Total pets' as metric,
  COUNT(*) as count
FROM pets
UNION ALL
SELECT 
  'Pets with only creator access' as metric,
  COUNT(*) as count
FROM pets p
WHERE NOT EXISTS (
  SELECT 1 FROM pet_owners po 
  WHERE po.pet_id = p.id AND po.user_id != p.created_by
)
UNION ALL
SELECT 
  'Pets shared with others' as metric,
  COUNT(*) as count
FROM pets p
WHERE EXISTS (
  SELECT 1 FROM pet_owners po 
  WHERE po.pet_id = p.id AND po.user_id != p.created_by
);

-- Check 7: Verify share tokens are properly restricted
SELECT 
  COUNT(*) as total_share_tokens,
  COUNT(CASE WHEN is_active = true THEN 1 END) as active_tokens,
  COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as non_expired_tokens
FROM pet_share_tokens;

SELECT 'Privacy verification complete' as status;
