-- Alternative approach: Change foreign keys to reference auth.users directly
-- This eliminates the dependency on profiles table for core functionality

-- Step 1: Disable <PERSON><PERSON> temporarily to avoid conflicts
ALTER TABLE pets DISABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners DISABLE ROW LEVEL SECURITY;
ALTER TABLE feeding_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE litter_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE vaccinations DISABLE ROW LEVEL SECURITY;
ALTER TABLE notes DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop existing foreign key constraints
ALTER TABLE pets DROP CONSTRAINT IF EXISTS pets_created_by_fkey;
ALTER TABLE pet_owners DROP CONSTRAINT IF EXISTS pet_owners_user_id_fkey;
ALTER TABLE feeding_logs DROP CONSTRAINT IF EXISTS feeding_logs_user_id_fkey;
ALTER TABLE litter_logs DROP CONSTRAINT IF EXISTS litter_logs_user_id_fkey;
ALTER TABLE vaccinations DROP CONSTRAINT IF EXISTS vaccinations_user_id_fkey;
ALTER TABLE notes DROP CONSTRAINT IF EXISTS notes_user_id_fkey;

-- Step 3: Create new foreign key constraints referencing auth.users
ALTER TABLE pets 
ADD CONSTRAINT pets_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE pet_owners 
ADD CONSTRAINT pet_owners_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE feeding_logs 
ADD CONSTRAINT feeding_logs_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE litter_logs 
ADD CONSTRAINT litter_logs_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE vaccinations 
ADD CONSTRAINT vaccinations_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE notes 
ADD CONSTRAINT notes_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Step 4: Create simple RLS policies using auth.uid() directly
DROP POLICY IF EXISTS "pets_select_policy" ON pets;
DROP POLICY IF EXISTS "pets_insert_policy" ON pets;
DROP POLICY IF EXISTS "pets_update_policy" ON pets;
DROP POLICY IF EXISTS "pets_delete_policy" ON pets;
DROP POLICY IF EXISTS "pet_owners_all_policy" ON pet_owners;
DROP POLICY IF EXISTS "feeding_logs_policy" ON feeding_logs;
DROP POLICY IF EXISTS "litter_logs_policy" ON litter_logs;
DROP POLICY IF EXISTS "vaccinations_policy" ON vaccinations;
DROP POLICY IF EXISTS "notes_policy" ON notes;

-- Pets policies
CREATE POLICY "pets_select_policy" ON pets
  FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "pets_insert_policy" ON pets
  FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "pets_update_policy" ON pets
  FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "pets_delete_policy" ON pets
  FOR DELETE USING (created_by = auth.uid());

-- Pet_owners policies
CREATE POLICY "pet_owners_all_policy" ON pet_owners
  FOR ALL USING (user_id = auth.uid());

-- Other table policies
CREATE POLICY "feeding_logs_policy" ON feeding_logs
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "litter_logs_policy" ON litter_logs
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "vaccinations_policy" ON vaccinations
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "notes_policy" ON notes
  FOR ALL USING (user_id = auth.uid());

-- Step 5: Re-enable RLS
ALTER TABLE pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners ENABLE ROW LEVEL SECURITY;
ALTER TABLE feeding_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE litter_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE vaccinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;

SELECT 'Foreign keys updated to reference auth.users directly' as status;
