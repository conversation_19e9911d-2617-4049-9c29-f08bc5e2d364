-- Emergency fix to get the app working again
-- This temporarily disables <PERSON><PERSON> to allow the app to load

-- Step 1: Temporarily disable <PERSON><PERSON> on all tables to get app working
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE pets DISABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners DISABLE ROW LEVEL SECURITY;
ALTER TABLE feeding_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE litter_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE vaccinations DISABLE ROW LEVEL SECURITY;
ALTER TABLE notes DISABLE ROW LEVEL SECURITY;

-- Step 2: Create any missing profiles for auth users
INSERT INTO public.profiles (id, email, full_name, created_at, updated_at)
SELECT 
  au.id,
  au.email,
  COALESCE(au.raw_user_meta_data->>'full_name', 'User'),
  au.created_at,
  NOW()
FROM auth.users au
LEFT JOIN public.profiles p ON au.id = p.id
WHERE p.id IS NULL
ON CONFLICT (id) DO NOTHING;

-- Step 3: Ensure foreign keys reference auth.users (safer approach)
ALTER TABLE pets DROP CONSTRAINT IF EXISTS pets_created_by_fkey;
ALTER TABLE pets 
ADD CONSTRAINT pets_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Step 4: Create minimal, working RLS policies
CREATE POLICY "allow_all_for_authenticated_users" ON profiles
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "allow_all_for_authenticated_users" ON pets
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "allow_all_for_authenticated_users" ON pet_owners
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "allow_all_for_authenticated_users" ON feeding_logs
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "allow_all_for_authenticated_users" ON litter_logs
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "allow_all_for_authenticated_users" ON vaccinations
  FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "allow_all_for_authenticated_users" ON notes
  FOR ALL USING (auth.role() = 'authenticated');

-- Step 5: Re-enable RLS with permissive policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners ENABLE ROW LEVEL SECURITY;
ALTER TABLE feeding_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE litter_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE vaccinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;

SELECT 'Emergency fix applied - app should work now' as status;
