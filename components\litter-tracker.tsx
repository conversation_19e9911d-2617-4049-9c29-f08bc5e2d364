'use client';

import { useState, useEffect } from 'react';
import { petStorage } from '@/lib/storage';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Trash2, Plus, Calendar, Clock, Sparkles } from 'lucide-react';

interface LitterTrackerProps {
  petId: string;
  onUpdate?: () => void;
}

export function LitterTracker({ petId, onUpdate }: LitterTrackerProps) {
  const { toast } = useToast();
  const [logs, setLogs] = useState<any[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newLog, setNewLog] = useState({
    type: '' as 'scoop' | 'deep-clean' | 'full-change' | '',
    notes: ''
  });

  useEffect(() => {
    loadLogs();
  }, [petId]);

  const loadLogs = async () => {
    setLoading(true);
    try {
      const litterLogs = await petStorage.getLitterLogs(petId);
      setLogs(litterLogs);
    } catch (error) {
      console.error('Error loading litter logs:', error);
      toast({
        title: "Error",
        description: "Failed to load litter logs",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddLog = async () => {
    if (!newLog.type) {
      toast({
        title: "Error",
        description: "Please select a litter maintenance type",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const log = await petStorage.addLitterLog({
        pet_id: petId,
        timestamp: new Date().toISOString(),
        type: newLog.type,
        notes: newLog.notes || null
      });

      if (log) {
        await loadLogs();
        onUpdate?.();
        setNewLog({ type: '', notes: '' });
        setIsDialogOpen(false);

        toast({
          title: "Litter maintenance logged!",
          description: `Added ${log.type.replace('-', ' ')} record.`,
        });
      }
    } catch (error) {
      console.error('Error adding litter log:', error);
      toast({
        title: "Error",
        description: "Failed to add litter log",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteLog = async (id: string) => {
    setLoading(true);
    try {
      const success = await petStorage.removeLitterLog(id);
      if (success) {
        await loadLogs();
        onUpdate?.();
        toast({
          title: "Success",
          description: "Litter log deleted",
        });
      }
    } catch (error) {
      console.error('Error deleting litter log:', error);
      toast({
        title: "Error",
        description: "Failed to delete litter log",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getTodayLogs = () => {
    const today = new Date().toDateString();
    return logs.filter(log => new Date(log.timestamp).toDateString() === today);
  };

  const getTypeDetails = (type: string) => {
    switch (type) {
      case 'scoop':
        return { label: 'Scooped', color: 'bg-blue-100 text-blue-800 border-blue-200', icon: '🥄' };
      case 'deep-clean':
        return { label: 'Deep Clean', color: 'bg-green-100 text-green-800 border-green-200', icon: '🧽' };
      case 'full-change':
        return { label: 'Full Change', color: 'bg-purple-100 text-purple-800 border-purple-200', icon: '🔄' };
      default:
        return { label: type, color: 'bg-gray-100 text-gray-800 border-gray-200', icon: '📦' };
    }
  };

  const getLastCleanTime = () => {
    if (logs.length === 0) return null;
    const lastLog = logs[0];
    const timeDiff = Date.now() - new Date(lastLog.timestamp).getTime();
    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
    
    if (hours < 1) return 'Less than an hour ago';
    if (hours === 1) return '1 hour ago';
    if (hours < 24) return `${hours} hours ago`;
    
    const days = Math.floor(hours / 24);
    if (days === 1) return '1 day ago';
    return `${days} days ago`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Litter Box Tracker</h2>
          <p className="text-gray-600">Keep track of litter box maintenance and cleaning</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Log Cleaning
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Record Litter Box Cleaning</DialogTitle>
              <DialogDescription>
                Add details about the cleaning activity
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="type">Cleaning Type *</Label>
                <Select value={newLog.type} onValueChange={(value: any) => setNewLog({ ...newLog, type: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select cleaning type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scoop">🥄 Scooped - Removed waste only</SelectItem>
                    <SelectItem value="deep-clean">🧽 Deep Clean - Cleaned with soap/disinfectant</SelectItem>
                    <SelectItem value="full-change">🔄 Full Change - Replaced all litter</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="notes">Notes (optional)</Label>
                <Textarea
                  id="notes"
                  value={newLog.notes}
                  onChange={(e) => setNewLog({ ...newLog, notes: e.target.value })}
                  placeholder="Any observations or additional details..."
                  className="min-h-[80px]"
                />
              </div>
              <div className="flex space-x-2 pt-4">
                <Button onClick={handleAddLog} className="flex-1" disabled={!newLog.type || loading}>
                  Record Cleaning
                </Button>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-100 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Today&apos;s Cleanings</p>
                <p className="text-2xl font-bold text-gray-900">{getTodayLogs().length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-green-100 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Last Cleaned</p>
                <p className="text-lg font-semibold text-gray-900">
                  {getLastCleanTime() || 'Never'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-purple-100 p-3 rounded-lg">
                <Sparkles className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">This Week</p>
                <p className="text-2xl font-bold text-gray-900">
                  {logs.filter(log => {
                    const logDate = new Date(log.timestamp);
                    const weekAgo = new Date();
                    weekAgo.setDate(weekAgo.getDate() - 7);
                    return logDate > weekAgo;
                  }).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Today's Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Today&apos;s Activity</span>
          </CardTitle>
          <CardDescription>
            Litter box cleanings recorded today
          </CardDescription>
        </CardHeader>
        <CardContent>
          {getTodayLogs().length === 0 ? (
            <p className="text-gray-500 text-center py-4">No cleanings recorded today</p>
          ) : (
            <div className="space-y-3">
              {getTodayLogs().map((log) => {
                const { time } = formatTimestamp(log.timestamp);
                const typeDetails = getTypeDetails(log.type);
                return (
                  <div key={log.id} className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Badge variant="secondary" className={typeDetails.color}>
                        {typeDetails.icon} {typeDetails.label}
                      </Badge>
                      <span className="text-sm text-gray-600">{time}</span>
                    </div>
                    {log.notes && (
                      <p className="text-sm text-gray-600">{log.notes}</p>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* All Cleaning Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Trash2 className="h-5 w-5" />
            <span>Cleaning History</span>
          </CardTitle>
          <CardDescription>
            Complete record of all litter box maintenance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {logs.length === 0 ? (
            <div className="text-center py-8">
              <Trash2 className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <p className="text-gray-500">No cleaning logs yet</p>
              <p className="text-sm text-gray-400">Record your first litter box cleaning to get started</p>
            </div>
          ) : (
            <div className="space-y-4">
              {logs.map((log) => {
                const { date, time } = formatTimestamp(log.timestamp);
                const typeDetails = getTypeDetails(log.type);
                return (
                  <div key={log.id} className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <Badge variant="outline" className={typeDetails.color}>
                          {typeDetails.icon} {typeDetails.label}
                        </Badge>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>{date}</span>
                          <Clock className="h-4 w-4" />
                          <span>{time}</span>
                        </div>
                      </div>
                      {log.notes && (
                        <p className="text-sm text-gray-600">{log.notes}</p>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteLog(log.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}