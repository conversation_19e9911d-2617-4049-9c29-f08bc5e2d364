-- Migration to add nickname field and character limits to pets table
-- Run this in Supabase SQL Editor

-- Add nickname column to pets table
ALTER TABLE pets ADD COLUMN IF NOT EXISTS nickname VARCHAR(100);

-- Add character limits to existing fields for better validation
-- Note: PostgreSQL TEXT fields don't have built-in limits, but we'll enforce them in the application
-- These comments document the intended limits:

-- Field limits to enforce in application:
-- name: 50 characters (reasonable for pet names)
-- nickname: 100 characters (can be longer/more descriptive)
-- species: 30 characters
-- breed: 50 characters  
-- weight: 20 characters (e.g., "15 lbs", "7.5 kg")
-- color: 30 characters
-- notes: 1000 characters (longer text field)

-- Update the updated_at timestamp when pets are modified
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS update_pets_updated_at ON pets;
CREATE TRIGGER update_pets_updated_at
    BEFORE UPDATE ON pets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
