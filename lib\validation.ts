// Validation utilities for pet data

export interface PetFieldLimits {
  name: number
  nickname: number
  species: number
  breed: number
  weight: number
  color: number
  notes: number
}

export const PET_FIELD_LIMITS: PetFieldLimits = {
  name: 50,
  nickname: 100,
  species: 30,
  breed: 50,
  weight: 20,
  color: 30,
  notes: 1000
}

export interface ValidationError {
  field: string
  message: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

export function validatePetData(data: {
  name: string
  nickname?: string
  species: string
  breed?: string
  weight?: string
  color?: string
  notes?: string
}): ValidationResult {
  const errors: ValidationError[] = []

  // Required field validation
  if (!data.name?.trim()) {
    errors.push({ field: 'name', message: 'Name is required' })
  } else if (data.name.trim().length > PET_FIELD_LIMITS.name) {
    errors.push({ 
      field: 'name', 
      message: `Name must be ${PET_FIELD_LIMITS.name} characters or less` 
    })
  }

  if (!data.species?.trim()) {
    errors.push({ field: 'species', message: 'Species is required' })
  } else if (data.species.trim().length > PET_FIELD_LIMITS.species) {
    errors.push({ 
      field: 'species', 
      message: `Species must be ${PET_FIELD_LIMITS.species} characters or less` 
    })
  }

  // Optional field validation
  if (data.nickname && data.nickname.trim().length > PET_FIELD_LIMITS.nickname) {
    errors.push({ 
      field: 'nickname', 
      message: `Nickname must be ${PET_FIELD_LIMITS.nickname} characters or less` 
    })
  }

  if (data.breed && data.breed.trim().length > PET_FIELD_LIMITS.breed) {
    errors.push({ 
      field: 'breed', 
      message: `Breed must be ${PET_FIELD_LIMITS.breed} characters or less` 
    })
  }

  if (data.weight && data.weight.trim().length > PET_FIELD_LIMITS.weight) {
    errors.push({ 
      field: 'weight', 
      message: `Weight must be ${PET_FIELD_LIMITS.weight} characters or less` 
    })
  }

  if (data.color && data.color.trim().length > PET_FIELD_LIMITS.color) {
    errors.push({ 
      field: 'color', 
      message: `Color must be ${PET_FIELD_LIMITS.color} characters or less` 
    })
  }

  if (data.notes && data.notes.trim().length > PET_FIELD_LIMITS.notes) {
    errors.push({ 
      field: 'notes', 
      message: `Notes must be ${PET_FIELD_LIMITS.notes} characters or less` 
    })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export function getCharacterCount(text: string): number {
  return text?.length || 0
}

export function getRemainingCharacters(text: string, limit: number): number {
  return Math.max(0, limit - getCharacterCount(text))
}

export function isFieldValid(text: string, limit: number, required: boolean = false): boolean {
  if (required && !text?.trim()) {
    return false
  }
  return getCharacterCount(text) <= limit
}
