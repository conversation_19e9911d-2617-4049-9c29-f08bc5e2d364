import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { createClient } from '@supabase/supabase-js'

// Create a server-side Supabase client for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const { token } = await params

    // Validate the share token using the database function
    const { data: validationResult, error: validationError } = await supabaseAdmin
      .rpc('validate_share_token', { token_value: token })

    if (validationError) {
      return NextResponse.json({ error: 'Failed to validate token' }, { status: 500 })
    }

    if (!validationResult || validationResult.length === 0) {
      return NextResponse.json({ 
        error: 'Invalid or expired share link',
        isValid: false 
      }, { status: 404 })
    }

    const result = validationResult[0]

    if (!result.is_valid) {
      return NextResponse.json({ 
        error: result.error_message || 'Invalid share link',
        isValid: false 
      }, { status: 400 })
    }

    // Return the pet information for display
    return NextResponse.json({
      isValid: true,
      pet: {
        id: result.pet_id,
        name: result.pet_name,
        species: result.pet_species
      },
      owner: {
        name: result.owner_name,
        email: result.owner_email
      },
      tokenId: result.token_id
    })

  } catch (error) {
    console.error('Error validating share token:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const { token } = await params

    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create a client with the user's token
    const userToken = authHeader.replace('Bearer ', '')
    const userSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )
    
    await userSupabase.auth.setSession({
      access_token: userToken,
      refresh_token: ''
    })

    // Get the current user
    const { data: { user }, error: userError } = await userSupabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First validate the token
    const { data: validationResult, error: validationError } = await supabaseAdmin
      .rpc('validate_share_token', { token_value: token })

    if (validationError || !validationResult || validationResult.length === 0) {
      return NextResponse.json({ 
        error: 'Invalid or expired share link' 
      }, { status: 400 })
    }

    const result = validationResult[0]

    if (!result.is_valid) {
      return NextResponse.json({ 
        error: result.error_message || 'Invalid share link' 
      }, { status: 400 })
    }

    // Check if user already has access to this pet
    const { data: existingAccess, error: accessError } = await userSupabase
      .from('pet_owners')
      .select('id')
      .eq('pet_id', result.pet_id)
      .eq('user_id', user.id)
      .single()

    if (existingAccess) {
      return NextResponse.json({ 
        error: 'You already have access to this pet' 
      }, { status: 400 })
    }

    // Check if user is the creator of the pet
    const { data: petData, error: petError } = await userSupabase
      .from('pets')
      .select('created_by')
      .eq('id', result.pet_id)
      .single()

    if (petData && petData.created_by === user.id) {
      return NextResponse.json({ 
        error: 'You cannot share a pet with yourself' 
      }, { status: 400 })
    }

    // Use the token (increment usage count)
    const { data: useResult, error: useError } = await supabaseAdmin
      .rpc('use_share_token', { token_value: token })

    if (useError || !useResult) {
      return NextResponse.json({ 
        error: 'Failed to process share token' 
      }, { status: 500 })
    }

    // Add the user as a caretaker for the pet
    const { error: shareError } = await supabaseAdmin
      .from('pet_owners')
      .insert({
        pet_id: result.pet_id,
        user_id: user.id,
        role: 'caretaker'
      })

    if (shareError) {
      // If there's an error adding the user, we should ideally rollback the token usage
      // For now, we'll just return an error
      console.error('Error adding pet access:', shareError)
      return NextResponse.json({ 
        error: 'Failed to grant pet access' 
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `You now have access to ${result.pet_name}`,
      pet: {
        id: result.pet_id,
        name: result.pet_name,
        species: result.pet_species
      }
    })

  } catch (error) {
    console.error('Error accepting share:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
