'use client';

import { useState, useEffect } from 'react';
import { petStorage } from '@/lib/storage';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { StickyNote, Plus, Calendar, Clock, Trash2, Search, Filter } from 'lucide-react';

interface NotesTrackerProps {
  petId: string;
  onUpdate?: () => void;
}

export function NotesTracker({ petId, onUpdate }: NotesTrackerProps) {
  const { toast } = useToast();
  const [notes, setNotes] = useState<any[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [newNote, setNewNote] = useState({
    topic: '',
    category: '',
    description: ''
  });

  useEffect(() => {
    loadNotes();
  }, [petId]);

  const loadNotes = async () => {
    setLoading(true);
    try {
      const notesData = await petStorage.getNotes(petId);
      setNotes(notesData);
    } catch (error) {
      console.error('Error loading notes:', error);
      toast({
        title: "Error",
        description: "Failed to load notes",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async () => {
    if (!newNote.topic || !newNote.description) {
      toast({
        title: "Error",
        description: "Please enter topic and description",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const note = await petStorage.addNote({
        pet_id: petId,
        timestamp: new Date().toISOString(),
        topic: newNote.topic,
        category: newNote.category || 'general',
        description: newNote.description
      });

      if (note) {
        await loadNotes();
        onUpdate?.();
        setNewNote({ topic: '', category: '', description: '' });
        setIsDialogOpen(false);

        toast({
          title: "Note added!",
          description: `Added note: ${note.topic}`,
        });
      }
    } catch (error) {
      console.error('Error adding note:', error);
      toast({
        title: "Error",
        description: "Failed to add note",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    setLoading(true);
    try {
      const success = await petStorage.removeNote(id);
      if (success) {
        await loadNotes();
        onUpdate?.();
        toast({
          title: "Success",
          description: "Note deleted",
        });
      }
    } catch (error) {
      console.error('Error deleting note:', error);
      toast({
        title: "Error",
        description: "Failed to delete note",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      behavior: 'bg-blue-100 text-blue-800 border-blue-200',
      health: 'bg-red-100 text-red-800 border-red-200',
      diet: 'bg-orange-100 text-orange-800 border-orange-200',
      training: 'bg-green-100 text-green-800 border-green-200',
      grooming: 'bg-pink-100 text-pink-800 border-pink-200',
      vet: 'bg-purple-100 text-purple-800 border-purple-200',
      general: 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[category] || colors.general;
  };

  const getFilteredNotes = () => {
    return notes.filter(note => {
      const matchesSearch = searchTerm === '' || 
        note.topic.toLowerCase().includes(searchTerm.toLowerCase()) ||
        note.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = filterCategory === 'all' || note.category === filterCategory;
      
      return matchesSearch && matchesCategory;
    });
  };

  const getCategories = () => {
    const categories = Array.from(new Set(notes.map(n => n.category)));
    return categories.sort();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Notes & Observations</h2>
          <p className="text-gray-600">Keep track of important observations and notes about your pet</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-purple-600 hover:bg-purple-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Note
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Note</DialogTitle>
              <DialogDescription>
                Record an observation or note about your pet
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="topic">Topic/Title *</Label>
                <Input
                  id="topic"
                  value={newNote.topic}
                  onChange={(e) => setNewNote({ ...newNote, topic: e.target.value })}
                  placeholder="e.g., Strange behavior, New toy preference"
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={newNote.category} onValueChange={(value) => setNewNote({ ...newNote, category: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="behavior">🐾 Behavior</SelectItem>
                    <SelectItem value="health">❤️ Health</SelectItem>
                    <SelectItem value="diet">🍽️ Diet</SelectItem>
                    <SelectItem value="training">🎯 Training</SelectItem>
                    <SelectItem value="grooming">✂️ Grooming</SelectItem>
                    <SelectItem value="vet">🏥 Vet Visit</SelectItem>
                    <SelectItem value="general">📝 General</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={newNote.description}
                  onChange={(e) => setNewNote({ ...newNote, description: e.target.value })}
                  placeholder="Describe what you observed or want to remember..."
                  className="min-h-[100px]"
                />
              </div>
              <div className="flex space-x-2 pt-4">
                <Button onClick={handleAdd} className="flex-1" disabled={!newNote.topic || !newNote.description || loading}>
                  Add Note
                </Button>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search notes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {getCategories().map(category => (
                    <SelectItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-purple-100 p-3 rounded-lg">
                <StickyNote className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Notes</p>
                <p className="text-2xl font-bold text-gray-900">{notes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-100 p-3 rounded-lg">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">This Month</p>
                <p className="text-2xl font-bold text-gray-900">
                  {notes.filter(n => {
                    const noteDate = new Date(n.timestamp);
                    const now = new Date();
                    return noteDate.getMonth() === now.getMonth() && 
                           noteDate.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-green-100 p-3 rounded-lg">
                <Filter className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Categories</p>
                <p className="text-2xl font-bold text-gray-900">{getCategories().length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notes List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <StickyNote className="h-5 w-5" />
            <span>All Notes</span>
            {(searchTerm || filterCategory !== 'all') && (
              <Badge variant="secondary">
                {getFilteredNotes().length} of {notes.length}
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            {searchTerm && `Search results for "${searchTerm}"`}
            {filterCategory !== 'all' && ` • Filtered by ${filterCategory}`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {getFilteredNotes().length === 0 ? (
            <div className="text-center py-8">
              {notes.length === 0 ? (
                <>
                  <StickyNote className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                  <p className="text-gray-500">No notes recorded yet</p>
                  <p className="text-sm text-gray-400">Start adding observations about your pet</p>
                </>
              ) : (
                <>
                  <Search className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                  <p className="text-gray-500">No notes match your search</p>
                  <p className="text-sm text-gray-400">Try adjusting your search terms or filters</p>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {getFilteredNotes().map((note) => {
                const { date, time } = formatTimestamp(note.timestamp);
                return (
                  <div key={note.id} className="flex items-start justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-medium text-gray-900">{note.topic}</h3>
                        <Badge variant="outline" className={getCategoryColor(note.category)}>
                          {note.category}
                        </Badge>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>{date}</span>
                          <Clock className="h-4 w-4" />
                          <span>{time}</span>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 leading-relaxed">{note.description}</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(note.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 ml-4"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}