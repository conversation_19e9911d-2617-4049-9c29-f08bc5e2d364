'use client'

/**
 * Health check utilities to detect and resolve app loading issues
 */

export interface HealthCheckResult {
  status: 'healthy' | 'warning' | 'critical'
  issues: string[]
  recommendations: string[]
  loadingTime: number
}

export const healthCheck = {
  /**
   * Perform a comprehensive health check
   */
  check: (loading: boolean, user: any, loadingStartTime: number): HealthCheckResult => {
    const now = Date.now()
    const loadingTime = loading ? now - loadingStartTime : 0
    const issues: string[] = []
    const recommendations: string[] = []
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'

    // Check loading time
    if (loading && loadingTime > 10000) {
      status = 'critical'
      issues.push(`App has been loading for ${Math.round(loadingTime / 1000)}s`)
      recommendations.push('Clear browser cache and reload')
      recommendations.push('Check network connection')
      recommendations.push('Try incognito/private browsing mode')
    } else if (loading && loadingTime > 5000) {
      status = 'warning'
      issues.push(`Loading is taking longer than expected (${Math.round(loadingTime / 1000)}s)`)
      recommendations.push('Wait a bit longer or try refreshing')
    }

    // Check for authentication issues
    if (!loading && !user) {
      // This is normal - user needs to log in
    } else if (loading && typeof window !== 'undefined') {
      // Check for potential cache issues
      try {
        const supabaseKeys = Object.keys(window.localStorage || {}).filter(key => 
          key.startsWith('supabase.') || key.includes('supabase') || key.startsWith('sb-')
        )
        
        if (supabaseKeys.length > 10) {
          issues.push('Large number of cached authentication tokens detected')
          recommendations.push('Clear Supabase cache')
        }
      } catch (error) {
        issues.push('Unable to check localStorage')
        recommendations.push('Check browser permissions')
      }
    }

    // Check network connectivity
    if (typeof window !== 'undefined' && !navigator.onLine) {
      status = 'critical'
      issues.push('No internet connection detected')
      recommendations.push('Check your internet connection')
    }

    return {
      status,
      issues,
      recommendations,
      loadingTime
    }
  },

  /**
   * Auto-fix common issues
   */
  autoFix: async (): Promise<boolean> => {
    try {
      // Import cache utils dynamically to avoid SSR issues
      const { cacheUtils } = await import('./cache-utils')
      
      console.log('🔧 Attempting auto-fix...')
      
      // Clear Supabase cache first (less disruptive)
      const cleared = cacheUtils.clearSupabaseCache()
      
      if (cleared) {
        console.log('✓ Cleared Supabase cache')
        return true
      }
      
      return false
    } catch (error) {
      console.error('Auto-fix failed:', error)
      return false
    }
  },

  /**
   * Nuclear option - clear everything and reload
   */
  nuclearFix: async (): Promise<void> => {
    try {
      const { cacheUtils } = await import('./cache-utils')
      await cacheUtils.clearCacheAndReload()
    } catch (error) {
      console.error('Nuclear fix failed:', error)
      // Fallback to simple reload
      if (typeof window !== 'undefined') {
        window.location.reload()
      }
    }
  },

  /**
   * Get diagnostic information
   */
  getDiagnostics: () => {
    if (typeof window === 'undefined') return null

    return {
      userAgent: navigator.userAgent,
      online: navigator.onLine,
      cookieEnabled: navigator.cookieEnabled,
      localStorage: {
        available: !!window.localStorage,
        itemCount: window.localStorage ? Object.keys(window.localStorage).length : 0
      },
      sessionStorage: {
        available: !!window.sessionStorage,
        itemCount: window.sessionStorage ? Object.keys(window.sessionStorage).length : 0
      },
      url: window.location.href,
      timestamp: new Date().toISOString()
    }
  }
}

// Add to window for debugging
if (typeof window !== 'undefined') {
  (window as any).healthCheck = healthCheck
}
