-- Migration to add pet sharing tokens functionality
-- This enables private shareable URLs for pets

-- Create pet_share_tokens table
CREATE TABLE pet_share_tokens (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  token TEXT NOT NULL UNIQUE,
  pet_id UUID REFERENCES pets(id) ON DELETE CASCADE NOT NULL,
  created_by UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT true NOT NULL,
  usage_count INTEGER DEFAULT 0 NOT NULL,
  max_uses INTEGER DEFAULT NULL, -- NULL means unlimited uses
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster token lookups
CREATE INDEX idx_pet_share_tokens_token ON pet_share_tokens(token);
CREATE INDEX idx_pet_share_tokens_pet_id ON pet_share_tokens(pet_id);
CREATE INDEX idx_pet_share_tokens_created_by ON pet_share_tokens(created_by);
CREATE INDEX idx_pet_share_tokens_expires_at ON pet_share_tokens(expires_at);

-- Enable RLS on pet_share_tokens
ALTER TABLE pet_share_tokens ENABLE ROW LEVEL SECURITY;

-- RLS Policies for pet_share_tokens
-- Users can view tokens they created
CREATE POLICY "Users can view own share tokens" ON pet_share_tokens
  FOR SELECT USING (created_by = auth.uid());

-- Users can create tokens for pets they own
CREATE POLICY "Pet owners can create share tokens" ON pet_share_tokens
  FOR INSERT WITH CHECK (
    created_by = auth.uid() AND
    pet_id IN (
      SELECT p.id FROM pets p
      LEFT JOIN pet_owners po ON p.id = po.pet_id
      WHERE (p.created_by = auth.uid() OR (po.user_id = auth.uid() AND po.role = 'owner'))
    )
  );

-- Users can update tokens they created
CREATE POLICY "Users can update own share tokens" ON pet_share_tokens
  FOR UPDATE USING (created_by = auth.uid());

-- Users can delete tokens they created
CREATE POLICY "Users can delete own share tokens" ON pet_share_tokens
  FOR DELETE USING (created_by = auth.uid());

-- Function to generate a secure random token
CREATE OR REPLACE FUNCTION generate_share_token()
RETURNS TEXT AS $$
BEGIN
  -- Generate a URL-safe random token (32 characters)
  RETURN encode(gen_random_bytes(24), 'base64')::text;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired tokens (can be called periodically)
CREATE OR REPLACE FUNCTION cleanup_expired_share_tokens()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM pet_share_tokens 
  WHERE expires_at < NOW() OR (max_uses IS NOT NULL AND usage_count >= max_uses);
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to validate and use a share token
CREATE OR REPLACE FUNCTION validate_share_token(token_value TEXT)
RETURNS TABLE(
  token_id UUID,
  pet_id UUID,
  pet_name TEXT,
  pet_species TEXT,
  owner_name TEXT,
  owner_email TEXT,
  is_valid BOOLEAN,
  error_message TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pst.id as token_id,
    p.id as pet_id,
    p.name as pet_name,
    p.species as pet_species,
    prof.full_name as owner_name,
    prof.email as owner_email,
    CASE 
      WHEN pst.id IS NULL THEN false
      WHEN NOT pst.is_active THEN false
      WHEN pst.expires_at < NOW() THEN false
      WHEN pst.max_uses IS NOT NULL AND pst.usage_count >= pst.max_uses THEN false
      ELSE true
    END as is_valid,
    CASE 
      WHEN pst.id IS NULL THEN 'Invalid or expired share link'
      WHEN NOT pst.is_active THEN 'This share link has been deactivated'
      WHEN pst.expires_at < NOW() THEN 'This share link has expired'
      WHEN pst.max_uses IS NOT NULL AND pst.usage_count >= pst.max_uses THEN 'This share link has reached its usage limit'
      ELSE NULL
    END as error_message
  FROM pet_share_tokens pst
  LEFT JOIN pets p ON pst.pet_id = p.id
  LEFT JOIN profiles prof ON pst.created_by = prof.id
  WHERE pst.token = token_value;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment usage count when token is used
CREATE OR REPLACE FUNCTION use_share_token(token_value TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  token_record RECORD;
BEGIN
  -- First validate the token
  SELECT * INTO token_record FROM validate_share_token(token_value) LIMIT 1;
  
  IF NOT token_record.is_valid THEN
    RETURN false;
  END IF;
  
  -- Increment usage count
  UPDATE pet_share_tokens 
  SET usage_count = usage_count + 1, updated_at = NOW()
  WHERE token = token_value;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add updated_at trigger for pet_share_tokens
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_pet_share_tokens_updated_at
  BEFORE UPDATE ON pet_share_tokens
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
