import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { createClient } from '@supabase/supabase-js'

// Create a server-side Supabase client for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ petId: string }> }
) {
  try {
    const { petId } = await params
    const body = await request.json()
    const { expiresInDays = 30, maxUses = null } = body

    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create a client with the user's token
    const token = authHeader.replace('Bearer ', '')
    const userSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )
    
    await userSupabase.auth.setSession({
      access_token: token,
      refresh_token: ''
    })

    // Get the current user
    const { data: { user }, error: userError } = await userSupabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the user owns or has owner access to the pet
    const { data: petAccess, error: petError } = await userSupabase
      .from('pets')
      .select(`
        id,
        created_by,
        pet_owners!inner(user_id, role)
      `)
      .eq('id', petId)
      .single()

    if (petError || !petAccess) {
      return NextResponse.json({ error: 'Pet not found' }, { status: 404 })
    }

    // Check if user is creator or owner
    const isCreator = petAccess.created_by === user.id
    const isOwner = petAccess.pet_owners.some((po: any) => 
      po.user_id === user.id && po.role === 'owner'
    )

    if (!isCreator && !isOwner) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Generate a secure token using the database function
    const { data: tokenData, error: tokenError } = await supabaseAdmin
      .rpc('generate_share_token')

    if (tokenError || !tokenData) {
      return NextResponse.json({ error: 'Failed to generate token' }, { status: 500 })
    }

    // Calculate expiration date
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + expiresInDays)

    // Create the share token record
    const { data: shareToken, error: insertError } = await supabaseAdmin
      .from('pet_share_tokens')
      .insert({
        token: tokenData,
        pet_id: petId,
        created_by: user.id,
        expires_at: expiresAt.toISOString(),
        max_uses: maxUses
      })
      .select()
      .single()

    if (insertError) {
      return NextResponse.json({ error: 'Failed to create share token' }, { status: 500 })
    }

    // Return the share URL
    const shareUrl = `${request.nextUrl.origin}/share/${tokenData}`
    
    return NextResponse.json({
      shareUrl,
      token: tokenData,
      expiresAt: expiresAt.toISOString(),
      maxUses
    })

  } catch (error) {
    console.error('Error creating share token:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ petId: string }> }
) {
  try {
    const { petId } = await params

    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create a client with the user's token
    const token = authHeader.replace('Bearer ', '')
    const userSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )
    
    await userSupabase.auth.setSession({
      access_token: token,
      refresh_token: ''
    })

    // Get the current user
    const { data: { user }, error: userError } = await userSupabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all active share tokens for this pet created by the user
    const { data: shareTokens, error: tokensError } = await userSupabase
      .from('pet_share_tokens')
      .select('*')
      .eq('pet_id', petId)
      .eq('created_by', user.id)
      .eq('is_active', true)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })

    if (tokensError) {
      return NextResponse.json({ error: 'Failed to fetch share tokens' }, { status: 500 })
    }

    // Add share URLs to the tokens
    const tokensWithUrls = shareTokens.map(token => ({
      ...token,
      shareUrl: `${request.nextUrl.origin}/share/${token.token}`
    }))

    return NextResponse.json({ shareTokens: tokensWithUrls })

  } catch (error) {
    console.error('Error fetching share tokens:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ petId: string }> }
) {
  try {
    const { petId } = await params
    const { searchParams } = new URL(request.url)
    const tokenId = searchParams.get('tokenId')

    if (!tokenId) {
      return NextResponse.json({ error: 'Token ID required' }, { status: 400 })
    }

    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Create a client with the user's token
    const token = authHeader.replace('Bearer ', '')
    const userSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )
    
    await userSupabase.auth.setSession({
      access_token: token,
      refresh_token: ''
    })

    // Get the current user
    const { data: { user }, error: userError } = await userSupabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Deactivate the share token (soft delete)
    const { error: updateError } = await userSupabase
      .from('pet_share_tokens')
      .update({ is_active: false })
      .eq('id', tokenId)
      .eq('pet_id', petId)
      .eq('created_by', user.id)

    if (updateError) {
      return NextResponse.json({ error: 'Failed to deactivate share token' }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error deactivating share token:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
