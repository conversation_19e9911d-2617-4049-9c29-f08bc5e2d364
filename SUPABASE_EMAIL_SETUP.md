# Supabase Email Confirmation Setup Guide

## Problem
When users sign up, they receive a confirmation email, but clicking the link results in:
```
{"error":"requested path is invalid"}
```

## Root Cause
The issue was caused by two problems:
1. Supabase needs to know where to redirect users after email confirmation
2. Your Next.js config had `output: 'export'` which prevents API routes from working

## Solution
1. **Removed static export** from `next.config.js` to enable API routes
2. **Created auth callback route** at `/auth/callback` to handle email confirmations

## Setup Steps

### 1. Update Supabase Auth Settings

Go to your Supabase Dashboard → Authentication → URL Configuration and update:

**Site URL:**
```
https://your-vercel-app.vercel.app
```

**Redirect URLs (add these):**
```
https://your-vercel-app.vercel.app/auth/callback
http://localhost:3001/auth/callback
```

**Note:** The local port is now 3001 since we removed static export.

### 2. Email Templates (Optional)

Go to Supabase Dashboard → Authentication → Email Templates

**Confirm signup template** should include:
```html
<h2>Confirm your signup</h2>
<p>Follow this link to confirm your user:</p>
<p><a href="{{ .ConfirmationURL }}">Confirm your account</a></p>
```

The `{{ .ConfirmationURL }}` will automatically use your configured redirect URL.

### 3. Test the Flow

1. **Sign up a new user** on your local development server (http://localhost:3001)
2. **Check email** for confirmation link
3. **Click the link** - it should redirect to `/auth/callback` and then to your app
4. **User should be automatically signed in**

### 4. Production Deployment

When deploying to Vercel:

1. **Update Supabase URLs** to use your production domain
2. **Test signup flow** on production
3. **Verify email confirmation** works end-to-end

## How It Works

1. **User signs up** → Supabase sends confirmation email
2. **User clicks link** → Redirects to `/auth/callback?code=...`
3. **Callback route** → Exchanges code for session
4. **Success** → Redirects to main app with user logged in
5. **Error** → Redirects to login with error message

## Files Created/Modified

- `app/auth/callback/route.ts` - Handles email confirmation
- `app/reset-password/page.tsx` - Password reset page
- `lib/auth.ts` - Updated signup with redirect URL
- `components/auth/login-form.tsx` - Better error handling

## Troubleshooting

**Still getting "requested path is invalid"?**
- Check that redirect URLs are correctly set in Supabase
- Ensure the callback route is deployed
- Verify environment variables are set

**Email not being sent?**
- Check Supabase logs for email delivery issues
- Verify SMTP settings if using custom email provider
- Check spam folder

**Callback errors?**
- Check browser console for detailed error messages
- Verify Supabase environment variables
- Check that the auth callback route is accessible

## Testing Locally

1. Start development server: `npm run dev`
2. Go to http://localhost:3001
3. Click "Sign Up" tab
4. Fill in details and submit
5. Check email for confirmation link
6. Click link - should redirect back to app with user logged in

## Production Checklist

- [ ] Supabase Site URL updated to production domain
- [ ] Redirect URLs include production callback URL
- [ ] Environment variables set in Vercel
- [ ] Email confirmation tested end-to-end
- [ ] Password reset flow tested (if implemented)
