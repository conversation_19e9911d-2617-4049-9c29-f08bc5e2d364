'use client'

/**
 * Utility functions for managing client-side cache and storage
 * to help resolve loading issues and cache-related problems
 */

export const cacheUtils = {
  /**
   * Clear all browser storage and caches
   */
  clearAllCache: async () => {
    try {
      // Clear localStorage
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.clear()
        console.log('✓ Cleared localStorage')
      }

      // Clear sessionStorage
      if (typeof window !== 'undefined' && window.sessionStorage) {
        window.sessionStorage.clear()
        console.log('✓ Cleared sessionStorage')
      }

      // Clear IndexedDB (used by Supabase for offline storage)
      if (typeof window !== 'undefined' && 'indexedDB' in window) {
        try {
          // Get all databases
          const databases = await indexedDB.databases()
          
          // Delete each database
          for (const db of databases) {
            if (db.name) {
              const deleteReq = indexedDB.deleteDatabase(db.name)
              await new Promise((resolve, reject) => {
                deleteReq.onsuccess = () => resolve(true)
                deleteReq.onerror = () => reject(deleteReq.error)
              })
              console.log(`✓ Cleared IndexedDB: ${db.name}`)
            }
          }
        } catch (error) {
          console.warn('Could not clear IndexedDB:', error)
        }
      }

      // Clear service worker caches
      if (typeof window !== 'undefined' && 'caches' in window) {
        try {
          const cacheNames = await caches.keys()
          await Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
          )
          console.log('✓ Cleared service worker caches')
        } catch (error) {
          console.warn('Could not clear service worker caches:', error)
        }
      }

      console.log('🧹 All caches cleared successfully')
      return true
    } catch (error) {
      console.error('Error clearing caches:', error)
      return false
    }
  },

  /**
   * Clear only Supabase-related storage
   */
  clearSupabaseCache: () => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const keys = Object.keys(window.localStorage)
        const supabaseKeys = keys.filter(key => 
          key.startsWith('supabase.') || 
          key.includes('supabase') ||
          key.startsWith('sb-')
        )
        
        supabaseKeys.forEach(key => {
          window.localStorage.removeItem(key)
          console.log(`✓ Removed: ${key}`)
        })
        
        console.log(`🧹 Cleared ${supabaseKeys.length} Supabase cache entries`)
        return true
      }
      return false
    } catch (error) {
      console.error('Error clearing Supabase cache:', error)
      return false
    }
  },

  /**
   * Get cache information for debugging
   */
  getCacheInfo: () => {
    if (typeof window === 'undefined') return null

    const info = {
      localStorage: {
        available: !!window.localStorage,
        itemCount: 0,
        supabaseKeys: [] as string[]
      },
      sessionStorage: {
        available: !!window.sessionStorage,
        itemCount: 0
      },
      indexedDB: {
        available: 'indexedDB' in window
      },
      serviceWorker: {
        available: 'serviceWorker' in navigator
      }
    }

    try {
      if (window.localStorage) {
        const keys = Object.keys(window.localStorage)
        info.localStorage.itemCount = keys.length
        info.localStorage.supabaseKeys = keys.filter(key => 
          key.startsWith('supabase.') || 
          key.includes('supabase') ||
          key.startsWith('sb-')
        )
      }

      if (window.sessionStorage) {
        info.sessionStorage.itemCount = Object.keys(window.sessionStorage).length
      }
    } catch (error) {
      console.warn('Error getting cache info:', error)
    }

    return info
  },

  /**
   * Force reload the page after clearing cache
   */
  clearCacheAndReload: async () => {
    await cacheUtils.clearAllCache()
    
    if (typeof window !== 'undefined') {
      // Use location.reload with force refresh
      window.location.reload()
    }
  },

  /**
   * Add debug information to console
   */
  debugCache: () => {
    const info = cacheUtils.getCacheInfo()
    console.group('🔍 Cache Debug Information')
    console.log('Cache Info:', info)
    
    if (typeof window !== 'undefined' && window.localStorage) {
      console.log('All localStorage keys:', Object.keys(window.localStorage))
    }
    
    console.groupEnd()
  }
}

// Add global cache utilities for debugging in browser console
if (typeof window !== 'undefined') {
  (window as any).cacheUtils = cacheUtils
}
