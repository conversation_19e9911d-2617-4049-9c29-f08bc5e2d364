-- Migration to fix pet access issues
-- Run this in your Supabase SQL Editor

-- 1. <PERSON><PERSON> function to automatically create pet ownership record when pet is created
CREATE OR REPLACE FUNCTION public.handle_new_pet()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.pet_owners (pet_id, user_id, role)
  VALUES (NEW.id, NEW.created_by, 'owner');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. <PERSON>reate trigger to automatically create pet ownership when pet is created
DROP TRIGGER IF EXISTS on_pet_created ON pets;
CREATE TRIGGER on_pet_created
  AFTER INSERT ON pets
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_pet();

-- 3. Fix existing pets that don't have ownership records
-- This will create owner records for all existing pets where the creator doesn't have an ownership record
INSERT INTO pet_owners (pet_id, user_id, role)
SELECT p.id, p.created_by, 'owner'
FROM pets p
LEFT JOIN pet_owners po ON p.id = po.pet_id AND p.created_by = po.user_id
WHERE po.id IS NULL;

-- 4. Verify the fix worked
-- This query should return all pets with their ownership information
SELECT 
  p.name,
  p.created_by,
  po.user_id,
  po.role,
  pr.email
FROM pets p
JOIN pet_owners po ON p.id = po.pet_id
JOIN profiles pr ON po.user_id = pr.id
ORDER BY p.created_at DESC;
