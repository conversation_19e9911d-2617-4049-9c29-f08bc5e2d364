-- Migration to restore proper privacy controls for pets
-- This fixes the overly permissive policies from migration_emergency_fix.sql
-- Run this in your Supabase SQL Editor

-- Step 1: Drop the overly permissive policies that allow all authenticated users access to everything
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON profiles;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON pets;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON pet_owners;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON feeding_logs;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON litter_logs;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON vaccinations;
DROP POLICY IF EXISTS "allow_all_for_authenticated_users" ON notes;

-- Step 2: Restore proper privacy-focused RLS policies for profiles
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Step 3: Create a helper function to check pet access without recursion
CREATE OR REPLACE FUNCTION public.user_has_pet_access(pet_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
  has_access BOOLEAN := FALSE;
BEGIN
  -- Check if user created the pet (direct check, no RLS)
  SELECT EXISTS(
    SELECT 1 FROM pets
    WHERE id = pet_id_param AND created_by = user_id_param
  ) INTO has_access;

  -- If not creator, check pet_owners table (direct check, no RLS)
  IF NOT has_access THEN
    SELECT EXISTS(
      SELECT 1 FROM pet_owners
      WHERE pet_id = pet_id_param AND user_id = user_id_param
    ) INTO has_access;
  END IF;

  RETURN has_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Create non-recursive RLS policies using the helper function
CREATE POLICY "pets_select_policy" ON pets
  FOR SELECT USING (
    public.user_has_pet_access(id, auth.uid())
  );

CREATE POLICY "pets_insert_policy" ON pets
  FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "pets_update_policy" ON pets
  FOR UPDATE USING (
    created_by = auth.uid() OR
    EXISTS(
      SELECT 1 FROM pet_owners
      WHERE pet_id = id AND user_id = auth.uid() AND role = 'owner'
    )
  );

CREATE POLICY "pets_delete_policy" ON pets
  FOR DELETE USING (created_by = auth.uid());

-- Step 5: Create simple pet_owners policies (no cross-references)
CREATE POLICY "pet_owners_select_policy" ON pet_owners
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid())
  );

CREATE POLICY "pet_owners_insert_policy" ON pet_owners
  FOR INSERT WITH CHECK (
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid())
  );

CREATE POLICY "pet_owners_update_policy" ON pet_owners
  FOR UPDATE USING (
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid())
  );

CREATE POLICY "pet_owners_delete_policy" ON pet_owners
  FOR DELETE USING (
    EXISTS(SELECT 1 FROM pets WHERE id = pet_id AND created_by = auth.uid())
  );

-- Step 6: Create policies for feeding_logs using the helper function
CREATE POLICY "feeding_logs_select_policy" ON feeding_logs
  FOR SELECT USING (
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "feeding_logs_insert_policy" ON feeding_logs
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "feeding_logs_update_policy" ON feeding_logs
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "feeding_logs_delete_policy" ON feeding_logs
  FOR DELETE USING (user_id = auth.uid());

-- Step 7: Create policies for litter_logs using the helper function
CREATE POLICY "litter_logs_select_policy" ON litter_logs
  FOR SELECT USING (
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "litter_logs_insert_policy" ON litter_logs
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "litter_logs_update_policy" ON litter_logs
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "litter_logs_delete_policy" ON litter_logs
  FOR DELETE USING (user_id = auth.uid());

-- Step 8: Create policies for vaccinations using the helper function
CREATE POLICY "vaccinations_select_policy" ON vaccinations
  FOR SELECT USING (
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "vaccinations_insert_policy" ON vaccinations
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "vaccinations_update_policy" ON vaccinations
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "vaccinations_delete_policy" ON vaccinations
  FOR DELETE USING (user_id = auth.uid());

-- Step 9: Create policies for notes using the helper function
CREATE POLICY "notes_select_policy" ON notes
  FOR SELECT USING (
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "notes_insert_policy" ON notes
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND
    public.user_has_pet_access(pet_id, auth.uid())
  );

CREATE POLICY "notes_update_policy" ON notes
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "notes_delete_policy" ON notes
  FOR DELETE USING (user_id = auth.uid());

-- Step 10: Verify that RLS is enabled on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE pet_owners ENABLE ROW LEVEL SECURITY;
ALTER TABLE feeding_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE litter_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE vaccinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;

-- Step 11: Ensure the pet ownership trigger is working
CREATE OR REPLACE FUNCTION public.handle_new_pet()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.pet_owners (pet_id, user_id, role)
  VALUES (NEW.id, NEW.created_by, 'owner');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS on_pet_created ON pets;
CREATE TRIGGER on_pet_created
  AFTER INSERT ON pets
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_pet();

SELECT 'Privacy controls restored - pets are now private by default' as status;
